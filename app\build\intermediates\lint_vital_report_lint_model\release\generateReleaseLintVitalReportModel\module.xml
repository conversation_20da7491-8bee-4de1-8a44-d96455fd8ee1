<lint-module
    format="1"
    dir="D:\PROJECTS\alarmtest\app"
    name=":app"
    type="APP"
    maven="AlarmTest:app:unspecified"
    agpVersion="8.9.1"
    buildFolder="build"
    bootClassPath="D:\AndroidSDK\platforms\android-35\android.jar;D:\AndroidSDK\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
