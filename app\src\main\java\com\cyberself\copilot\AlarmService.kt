package com.cyberself.copilot

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat

class AlarmService : Service() {
    private val TAG = "AlarmService"
    private val CHANNEL_ID = "alarm_channel"
    private val NOTIFICATION_ID = 1

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Show a foreground notification to keep service alive
        val notification = createNotification(
            "Alarm Service",
            "Managing your alarms..."
        )
        startForeground(NOTIFICATION_ID, notification)

        when (intent?.action) {
            "com.cyberself.copilot.START_ALARM" -> {
                val alarmData = intent.getStringExtra("ALARM_DATA") ?: return START_NOT_STICKY
                Log.d(TAG, "AlarmService starting alarm with data: $alarmData")

                // Multi-strategy approach for universal compatibility
                executeUniversalAlarmStrategy(alarmData)

                // Keep service running longer to ensure alarm is handled
                Handler(Looper.getMainLooper()).postDelayed({
                    Log.d(TAG, "Stopping alarm service after delay")
                    stopSelf()
                }, 12000) // Increased to 12 seconds for maximum reliability
            }

            "com.cyberself.copilot.RESTORE_ALARMS" -> {
                Log.d(TAG, "Service received restore alarms command")
                Thread {
                    try {
                        restoreAlarms()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error restoring alarms: ${e.message}", e)
                    } finally {
                        stopSelf(startId)
                    }
                }.start()
                return START_STICKY
            }
        }

        return START_STICKY
    }

    private fun restoreAlarms() {
        Log.d(TAG, "Restoring alarms from service")
        // Retrieve all saved alarms from SharedPreferences
        val alarms = getAlarmsFromPrefs()
        val currentTime = System.currentTimeMillis()
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager

        Log.d(TAG, "Found ${alarms.size} alarms to restore")

        for (alarm in alarms) {
            val (timestamp, dataJson) = alarm

            // Skip alarms that have already passed
            if (timestamp <= currentTime) {
                continue
            }

            // Create intent for the alarm receiver
            val alarmIntent = Intent(this, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
                putExtra("ALARM_DATA", dataJson)
            }

            // Create unique request code based on alarm time
            val requestCode = timestamp.hashCode()

            val pendingIntent = PendingIntent.getBroadcast(
                this,
                requestCode,
                alarmIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Reschedule the alarm
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (alarmManager.canScheduleExactAlarms()) {
                        alarmManager.setExactAndAllowWhileIdle(
                            AlarmManager.RTC_WAKEUP,
                            timestamp,
                            pendingIntent
                        )
                        Log.d(TAG, "Rescheduled alarm for ${java.util.Date(timestamp)}")
                    } else {
                        Log.w(TAG, "Cannot schedule exact alarms - permission not granted")
                    }
                } else {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        timestamp,
                        pendingIntent
                    )
                    Log.d(TAG, "Rescheduled alarm for ${java.util.Date(timestamp)}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to reschedule alarm: ${e.message}")
            }
        }
    }

    private fun getAlarmsFromPrefs(): List<Pair<Long, String>> {
        val result = mutableListOf<Pair<Long, String>>()
        val sharedPrefs = getSharedPreferences("ALARM_PREFS", Context.MODE_PRIVATE)

        for (key in sharedPrefs.all.keys) {
            if (key.startsWith("alarm_")) {
                val value = sharedPrefs.getString(key, null)
                value?.let {
                    val parts = it.split("|", limit = 2)
                    if (parts.size == 2) {
                        result.add(Pair(parts[0].toLong(), parts[1]))
                        Log.d(TAG, "Found saved alarm for ${java.util.Date(parts[0].toLong())}")
                    }
                }
            }
        }

        return result
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Alarm Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Used for alarm notifications"
                enableLights(true)
                lightColor = Color.RED
                enableVibration(true)
                setSound(null, null) // We'll play custom sounds in the activity
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(title: String, message: String): Notification {
        val fullScreenIntent = Intent(this, AlarmLauncherActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("LAUNCH_ALARM_SCREEN", true)
        }

        val fullScreenPendingIntent = PendingIntent.getActivity(
            this,
            0,
            fullScreenIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm) // Using a system icon
            .setPriority(NotificationCompat.PRIORITY_MAX) // Use MAX priority for alarms
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setFullScreenIntent(fullScreenPendingIntent, true) // This is key for direct launch
            .setAutoCancel(true) // Dismiss notification when activity is launched
            .setOngoing(true) // Required for foreground service
            .build()
    }

    private fun createAlarmNotification(alarmData: String): Notification {
        val fullScreenIntent = Intent(this, AlarmLauncherActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("LAUNCH_ALARM_SCREEN", true)
            putExtra("ALARM_DATA", alarmData)
        }

        val fullScreenPendingIntent = PendingIntent.getActivity(
            this,
            System.currentTimeMillis().toInt(), // Unique request code
            fullScreenIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Alarm Triggered!")
            .setContentText("Tap to view alarm")
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setFullScreenIntent(fullScreenPendingIntent, true) // Critical for background launch
            .setAutoCancel(false) // Don't auto-cancel for alarms
            .setOngoing(false) // Allow dismissal after alarm is handled
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()
    }

    private fun executeUniversalAlarmStrategy(alarmData: String) {
        Log.d(TAG, "Executing universal alarm strategy")

        // Strategy 1: High-priority notification with fullScreenIntent
        val alarmNotification = createAlarmNotification(alarmData)
        startForeground(NOTIFICATION_ID, alarmNotification)

        // Strategy 2: Direct activity launch (immediate)
        launchAlarmActivity(alarmData)

        // Strategy 3: Delayed retry (for stubborn devices)
        Handler(Looper.getMainLooper()).postDelayed({
            Log.d(TAG, "Executing delayed retry strategy")
            launchAlarmActivity(alarmData)
        }, 1000)

        // Strategy 4: Multiple notification attempts with different priorities
        Handler(Looper.getMainLooper()).postDelayed({
            Log.d(TAG, "Executing secondary notification strategy")
            val secondaryNotification = createUrgentAlarmNotification(alarmData)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID + 1, secondaryNotification)
        }, 2000)
    }

    private fun launchAlarmActivity(alarmData: String) {
        val launcherIntent = Intent(this, AlarmLauncherActivity::class.java).apply {
            addFlags(
                Intent.FLAG_ACTIVITY_NEW_TASK or
                Intent.FLAG_ACTIVITY_CLEAR_TOP or
                Intent.FLAG_ACTIVITY_SINGLE_TOP or
                Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS or
                Intent.FLAG_ACTIVITY_NO_HISTORY or
                Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT or
                Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
            )
            putExtra("LAUNCH_ALARM_SCREEN", true)
            putExtra("ALARM_DATA", alarmData)
        }

        try {
            startActivity(launcherIntent)
            Log.d(TAG, "AlarmLauncherActivity started from service")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start AlarmLauncherActivity: ${e.message}", e)
        }
    }

    private fun createUrgentAlarmNotification(alarmData: String): Notification {
        val fullScreenIntent = Intent(this, AlarmLauncherActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("LAUNCH_ALARM_SCREEN", true)
            putExtra("ALARM_DATA", alarmData)
        }

        val fullScreenPendingIntent = PendingIntent.getActivity(
            this,
            (System.currentTimeMillis() + 1000).toInt(), // Different request code
            fullScreenIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("⚠️ URGENT ALARM")
            .setContentText("Alarm requires attention")
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setFullScreenIntent(fullScreenPendingIntent, true)
            .setAutoCancel(false)
            .setOngoing(false)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setTimeoutAfter(30000) // Auto-dismiss after 30 seconds
            .build()
    }
}
