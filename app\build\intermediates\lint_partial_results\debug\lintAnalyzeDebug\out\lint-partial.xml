<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="ScheduleExactAlarm">
        <location id="0"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/AlarmService.kt"
            line="106"
            column="25"
            startOffset="3794"
            endLine="110"
            endColumn="26"
            endOffset="3993"/>
        <location id="1"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/AlarmService.kt"
            line="116"
            column="21"
            startOffset="4271"
            endLine="120"
            endColumn="22"
            endOffset="4454"/>
        <location id="2"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/BootReceiver.kt"
            line="50"
            column="25"
            startOffset="1874"
            endLine="54"
            endColumn="26"
            endOffset="2073"/>
        <location id="3"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/BootReceiver.kt"
            line="58"
            column="21"
            startOffset="2181"
            endLine="62"
            endColumn="22"
            endOffset="2364"/>
        <location id="4"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="160"
            column="25"
            startOffset="6088"
            endLine="160"
            endColumn="87"
            endOffset="6150"/>
        <location id="5"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="167"
            column="21"
            startOffset="6596"
            endLine="167"
            endColumn="83"
            endOffset="6658"/>
        <location id="6"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="173"
            column="17"
            startOffset="7009"
            endLine="177"
            endColumn="18"
            endOffset="7181"/>
        <entry
            name="ChecksExactAlarmPermission"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
                            line="235"
                            column="30"
                            startOffset="9819"
                            endLine="235"
                            endColumn="90"
                            endOffset="9879"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.cyberself.copilot.AlarmLauncherActivity"
                    boolean="true"/>
                <entry
                    name="com.cyberself.copilot.AlarmReceiver"
                    boolean="true"/>
                <entry
                    name="com.cyberself.copilot.BootReceiver"
                    boolean="true"/>
                <entry
                    name="com.cyberself.copilot.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.cyberself.copilot.TestAlarmActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="293"
            endLine="8"
            endColumn="24"
            endOffset="305"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="29"
            endOffset="79"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="109"
            endLine="4"
            endColumn="29"
            endOffset="126"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="29"
            endOffset="173"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="203"
            endLine="6"
            endColumn="27"
            endOffset="218"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="248"
            endLine="7"
            endColumn="27"
            endOffset="263"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="335"
            endLine="9"
            endColumn="24"
            endOffset="347"/>
        <entry
            name="model"
            string="color[purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),black(D),white(D)],drawable[ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U)],style[Theme_AlarmTest(U)],xml[data_extraction_rules(U),backup_rules(U)];8^9,a^7^8,b^7^8;;;"/>
    </map>

</incidents>
