<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="incidents">

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="46"
            column="13"
            startOffset="3005"
            endLine="46"
            endColumn="45"
            endOffset="3037"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.9.1 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.0"
                family="Update versions"
                oldString="8.9.1"
                replacement="8.10.0"
                priority="0"/>
            <fix-replace
                description="Change to 8.9.3"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.9.1"
                replacement="8.9.3"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="53"
            endLine="4"
            endColumn="19"
            endOffset="61"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="94"
            endLine="6"
            endColumn="23"
            endOffset="101"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="117"
            endLine="7"
            endColumn="23"
            endOffset="124"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="23"
            startOffset="147"
            endLine="8"
            endColumn="30"
            endOffset="154"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="173"
            endLine="9"
            endColumn="26"
            endOffset="180"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01">
        <fix-replace
            description="Change to 2025.05.01"
            family="Update versions"
            oldString="2024.09.00"
            replacement="2025.05.01"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="14"
            startOffset="194"
            endLine="10"
            endColumn="26"
            endOffset="206"/>
    </incident>

    <incident
        id="ExportedReceiver"
        severity="warning"
        message="Exported receiver does not require permission">
        <fix-attribute
            description="Set permission"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="permission"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="73"
            column="10"
            startOffset="4057"
            endLine="73"
            endColumn="18"
            endOffset="4065"/>
    </incident>

    <incident
        id="Wakelock"
        severity="warning"
        message="Wakelocks should be released in `onPause`, not `onDestroy`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/AlarmLauncherActivity.kt"
            line="137"
            column="17"
            startOffset="5040"
            endLine="137"
            endColumn="29"
            endOffset="5052"/>
    </incident>

    <incident
        id="Wakelock"
        severity="warning"
        message="Should not set both `PARTIAL_WAKE_LOCK` and `ACQUIRE_CAUSES_WAKEUP`. If you do not want the screen to turn on, get rid of `ACQUIRE_CAUSES_WAKEUP`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/AlarmReceiver.kt"
            line="27"
            column="17"
            startOffset="847"
            endLine="29"
            endColumn="46"
            endOffset="980"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/BootReceiver.kt"
            line="74"
            column="31"
            startOffset="2732"
            endLine="74"
            endColumn="77"
            endOffset="2778"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="157"
            column="17"
            startOffset="5844"
            endLine="157"
            endColumn="70"
            endOffset="5897"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="231"
            column="13"
            startOffset="9554"
            endLine="231"
            endColumn="59"
            endOffset="9600"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(&quot;package:$packageName&quot;)"
            replacement="&quot;package:$packageName&quot;.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
                startOffset="9915"
                endOffset="9948"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="236"
            column="28"
            startOffset="9915"
            endLine="236"
            endColumn="61"
            endOffset="9948"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(&quot;package:$packageName&quot;)"
            replacement="&quot;package:$packageName&quot;.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
                startOffset="9915"
                endOffset="9948"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="236"
            column="28"
            startOffset="9915"
            endLine="236"
            endColumn="61"
            endOffset="9948"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="53"
            column="21"
            startOffset="1849"
            endLine="53"
            endColumn="35"
            endOffset="1863"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="59"
            column="21"
            startOffset="2002"
            endLine="59"
            endColumn="42"
            endOffset="2023"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="64"
            column="21"
            startOffset="2150"
            endLine="64"
            endColumn="43"
            endOffset="2172"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="69"
            column="21"
            startOffset="2300"
            endLine="69"
            endColumn="42"
            endOffset="2321"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="74"
            column="21"
            startOffset="2450"
            endLine="74"
            endColumn="38"
            endOffset="2467"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="79"
            column="21"
            startOffset="2597"
            endLine="79"
            endColumn="51"
            endOffset="2627"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="84"
            column="21"
            startOffset="2776"
            endLine="84"
            endColumn="39"
            endOffset="2794"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="89"
            column="21"
            startOffset="2928"
            endLine="89"
            endColumn="49"
            endOffset="2956"/>
    </incident>

</incidents>
