package com.cyberself.copilot

import android.app.Activity
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.PowerManager
import android.util.Log
import android.net.Uri
import android.provider.Settings
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat

class TestAlarmActivity : Activity() {
    private val TAG = "TestAlarmActivity"
    
    private val ALARM_CHANNEL_ID = "test_alarm_channel"

    private val NOTIFICATION_PERMISSION_REQUEST_CODE = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        createAlarmNotificationChannel() // Create channel on app start
        
        // Request POST_NOTIFICATIONS permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), NOTIFICATION_PERMISSION_REQUEST_CODE)
            }
        }

        // Create simple UI
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }
        
        val title = TextView(this).apply {
            text = "Alarm Test App"
            textSize = 24f
            setPadding(0, 0, 0, 30)
        }
        
        val button0 = Button(this).apply {
            text = "Set Alarm (5 seconds)"
            setOnClickListener { setTestAlarm(5) }
        }

        val button1 = Button(this).apply {
            text = "Set Alarm (30 seconds)"
            setOnClickListener { setTestAlarm(30) }
        }
        
        val button2 = Button(this).apply {
            text = "Set Alarm (2 minutes)"
            setOnClickListener { setTestAlarm(120) }
        }
        
        val button3 = Button(this).apply {
            text = "Check Permissions"
            setOnClickListener { checkPermissions() }
        }

        val button4 = Button(this).apply {
            text = "Request Battery Opt. Exemption"
            setOnClickListener { requestBatteryOptimizationExemption() }
        }
        
        layout.addView(title)
        layout.addView(button0) // Add the new 5-second button
        layout.addView(button1)
        layout.addView(button2)
        layout.addView(button3)
        layout.addView(button4)
        
        setContentView(layout)
        
        Log.d(TAG, "TestAlarmActivity created")
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "Notification permission granted", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "Notification permission denied. Alarms may not show correctly.", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun setTestAlarm(seconds: Int) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmTime = System.currentTimeMillis() + (seconds * 1000)
            
            val testData = """{"message":"Test alarm triggered!","time":"${System.currentTimeMillis()}"}"""
            
            // Intent for AlarmReceiver to handle the actual alarm trigger
            val alarmReceiverIntent = Intent(this, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
                putExtra("ALARM_DATA", testData)
            }
            
            val requestCode = alarmTime.hashCode()
            val alarmPendingIntent = PendingIntent.getBroadcast(
                this,
                requestCode,
                alarmReceiverIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Intent for AlarmLauncherActivity to be shown as fullScreenIntent
            val fullScreenIntent = Intent(this, AlarmLauncherActivity::class.java).apply {
                putExtra("ALARM_DATA", testData)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }

            val fullScreenPendingIntent = PendingIntent.getActivity(
                this,
                requestCode + 1, // Use a different request code
                fullScreenIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Create a minimal notification for AlarmClockInfo
            val notification = createAlarmNotification(fullScreenPendingIntent)

            val alarmClockInfo = AlarmManager.AlarmClockInfo(alarmTime, fullScreenPendingIntent)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) { // setAlarmClock is API 21+
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (alarmManager.canScheduleExactAlarms()) {
                        alarmManager.setAlarmClock(alarmClockInfo, alarmPendingIntent)
                        Toast.makeText(this, "Alarm set for $seconds seconds (setAlarmClock)", Toast.LENGTH_SHORT).show()
                        Log.d(TAG, "Alarm scheduled for $seconds seconds from now using setAlarmClock")
                    } else {
                        Toast.makeText(this, "Cannot schedule exact alarms - permission needed", Toast.LENGTH_LONG).show()
                    }
                } else {
                    alarmManager.setAlarmClock(alarmClockInfo, alarmPendingIntent)
                    Toast.makeText(this, "Alarm set for $seconds seconds (setAlarmClock)", Toast.LENGTH_SHORT).show()
                    Log.d(TAG, "Alarm scheduled for $seconds seconds from now using setAlarmClock")
                }
            } else {
                // Fallback for older APIs if setAlarmClock is not available
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    alarmTime,
                    alarmPendingIntent
                )
                Toast.makeText(this, "Alarm set for $seconds seconds (fallback)", Toast.LENGTH_SHORT).show()
                Log.d(TAG, "Alarm scheduled for $seconds seconds from now using setExactAndAllowWhileIdle")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set alarm: ${e.message}", e)
            Toast.makeText(this, "Failed to set alarm: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun createAlarmNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                ALARM_CHANNEL_ID,
                "Test Alarm Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Channel for test alarms"
                setSound(null, null) // No sound from notification
                enableVibration(false) // No vibration from notification
                enableLights(false) // No lights from notification
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createAlarmNotification(fullScreenPendingIntent: PendingIntent): Notification {
        return NotificationCompat.Builder(this, ALARM_CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm) // Minimal icon
            .setContentTitle("Upcoming Alarm")
            .setContentText("Your alarm is set.")
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setFullScreenIntent(fullScreenPendingIntent, true) // This is the key
            .setAutoCancel(true)
            .build()
    }
    
    private fun checkPermissions() {
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val canScheduleExactAlarms = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true
        }
        
        val message = "Can schedule exact alarms: $canScheduleExactAlarms"
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.d(TAG, message)
    }

    private fun requestBatteryOptimizationExemption() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val packageName = packageName
            val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:$packageName")
                }
                try {
                    startActivity(intent)
                    Toast.makeText(this, "Please grant 'Ignore battery optimizations'", Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to launch battery optimization settings: ${e.message}", e)
                    Toast.makeText(this, "Could not open battery optimization settings. Please go to App Info -> Battery -> Optimize battery usage and set to 'Don't optimize' for this app.", Toast.LENGTH_LONG).show()
                }
            } else {
                Toast.makeText(this, "Already ignoring battery optimizations", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "Battery optimization exemption not applicable on this Android version", Toast.LENGTH_LONG).show()
        }
    }
}
