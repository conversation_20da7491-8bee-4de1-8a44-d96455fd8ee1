package com.cyberself.copilot

import android.app.Activity
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import android.net.Uri
import android.provider.Settings
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat

class TestAlarmActivity : Activity() {
    private val TAG = "TestAlarmActivity"

    private val ALARM_CHANNEL_ID = "test_alarm_channel"

    private val NOTIFICATION_PERMISSION_REQUEST_CODE = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        createAlarmNotificationChannel() // Create channel on app start

        // Auto-request critical permissions for universal compatibility
        requestCriticalPermissions()

        // Request POST_NOTIFICATIONS permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), NOTIFICATION_PERMISSION_REQUEST_CODE)
            }
        }

        // Create simple UI
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        val title = TextView(this).apply {
            text = "Alarm Test App"
            textSize = 24f
            setPadding(0, 0, 0, 30)
        }

        val button0 = Button(this).apply {
            text = "Set Alarm (5 seconds)"
            setOnClickListener { setTestAlarm(5) }
        }

        val button1 = Button(this).apply {
            text = "Set Alarm (30 seconds)"
            setOnClickListener { setTestAlarm(30) }
        }

        val button2 = Button(this).apply {
            text = "Set Alarm (2 minutes)"
            setOnClickListener { setTestAlarm(120) }
        }

        val button3 = Button(this).apply {
            text = "Check Permissions"
            setOnClickListener { checkPermissions() }
        }

        val button4 = Button(this).apply {
            text = "Request Battery Opt. Exemption"
            setOnClickListener { requestBatteryOptimizationExemption() }
        }

        val button5 = Button(this).apply {
            text = "Check All Permissions"
            setOnClickListener { debugAllSettings() }
        }

        layout.addView(title)
        layout.addView(button0)
        layout.addView(button1)
        layout.addView(button2)
        layout.addView(button3)
        layout.addView(button4)
        layout.addView(button5)

        setContentView(layout)

        Log.d(TAG, "TestAlarmActivity created")
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "Notification permission granted", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "Notification permission denied. Alarms may not show correctly.", Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * Sets a test alarm using the most reliable Android alarm APIs
     * Uses setAlarmClock() with fullScreenIntent for maximum compatibility
     */
    private fun setTestAlarm(seconds: Int) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmTime = System.currentTimeMillis() + (seconds * 1000)
            val testData = """{"message":"Test alarm triggered!","time":"${System.currentTimeMillis()}"}"""

            // Create broadcast intent for AlarmReceiver
            val alarmReceiverIntent = Intent(this, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
                putExtra("ALARM_DATA", testData)
            }

            val requestCode = alarmTime.hashCode()
            val alarmPendingIntent = PendingIntent.getBroadcast(
                this, requestCode, alarmReceiverIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Create fullScreenIntent for direct activity launch
            val fullScreenIntent = Intent(this, AlarmLauncherActivity::class.java).apply {
                putExtra("ALARM_DATA", testData)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }

            val fullScreenPendingIntent = PendingIntent.getActivity(
                this, requestCode + 1, fullScreenIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Use setAlarmClock() - most reliable method for exact alarms
            val alarmClockInfo = AlarmManager.AlarmClockInfo(alarmTime, fullScreenPendingIntent)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setAlarmClock(alarmClockInfo, alarmPendingIntent)
                    Toast.makeText(this, "Alarm set for $seconds seconds", Toast.LENGTH_SHORT).show()
                    Log.d(TAG, "Alarm scheduled using setAlarmClock")
                } else {
                    Toast.makeText(this, "Exact alarm permission needed", Toast.LENGTH_LONG).show()
                }
            } else {
                alarmManager.setAlarmClock(alarmClockInfo, alarmPendingIntent)
                Toast.makeText(this, "Alarm set for $seconds seconds", Toast.LENGTH_SHORT).show()
                Log.d(TAG, "Alarm scheduled using setAlarmClock")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to set alarm: ${e.message}", e)
            Toast.makeText(this, "Failed to set alarm", Toast.LENGTH_LONG).show()
        }
    }

    private fun createAlarmNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                ALARM_CHANNEL_ID,
                "Alarm System", // Minimal name
                NotificationManager.IMPORTANCE_HIGH // Must be HIGH for fullScreenIntent
            ).apply {
                description = "System alarm notifications"
                setSound(null, null) // No sound
                enableVibration(false) // No vibration
                enableLights(false) // No LED
                setShowBadge(false) // No app badge
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC // Show on lock screen
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createAlarmNotification(fullScreenPendingIntent: PendingIntent): Notification {
        return NotificationCompat.Builder(this, ALARM_CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm)
            .setContentTitle("") // Empty title for minimal appearance
            .setContentText("") // Empty text for minimal appearance
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setFullScreenIntent(fullScreenPendingIntent, true) // This launches the activity directly
            .setAutoCancel(true) // Auto-dismiss when fullScreenIntent triggers
            .setOngoing(false) // Allow dismissal
            .setShowWhen(false) // Don't show timestamp
            .setOnlyAlertOnce(true) // Only alert once
            .setSilent(true) // No sound/vibration from notification itself
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // Show on lock screen
            .build()
    }

    private fun checkPermissions() {
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val canScheduleExactAlarms = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true
        }

        val message = "Can schedule exact alarms: $canScheduleExactAlarms"
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.d(TAG, message)
    }

    private fun requestBatteryOptimizationExemption() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val packageName = packageName
            val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:$packageName")
                }
                try {
                    startActivity(intent)
                    Toast.makeText(this, "Please grant 'Ignore battery optimizations'", Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to launch battery optimization settings: ${e.message}", e)
                    Toast.makeText(this, "Could not open battery optimization settings. Please go to App Info -> Battery -> Optimize battery usage and set to 'Don't optimize' for this app.", Toast.LENGTH_LONG).show()
                }
            } else {
                Toast.makeText(this, "Already ignoring battery optimizations", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "Battery optimization exemption not applicable on this Android version", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Automatically requests critical permissions needed for universal alarm compatibility
     * Requests Display Over Apps and Battery Optimization exemptions with delays
     */
    private fun requestCriticalPermissions() {
        // Auto-request Display Over Apps permission for lock screen compatibility
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Handler(Looper.getMainLooper()).postDelayed({
                requestDisplayOverAppsPermission()
            }, 1000)
        }

        // Auto-request battery optimization exemption
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                Handler(Looper.getMainLooper()).postDelayed({
                    requestBatteryOptimizationExemption()
                }, 3000)
            }
        }
    }

    /**
     * Requests Display Over Apps permission for lock screen override capability
     */
    private fun requestDisplayOverAppsPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = Uri.parse("package:$packageName")
                }
                try {
                    startActivity(intent)
                    Toast.makeText(this, "Grant 'Display over other apps' for lock screen override", Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to launch overlay permission settings: ${e.message}", e)
                }
            } else {
                Toast.makeText(this, "Display over other apps permission already granted", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun debugAllSettings() {
        val sb = StringBuilder()

        // Check exact alarm permission
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val canScheduleExactAlarms = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true
        }
        sb.append("Exact Alarms: ${if (canScheduleExactAlarms) "✅" else "❌"}\n")

        // Check notification permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val hasNotificationPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED
            sb.append("Notifications: ${if (hasNotificationPermission) "✅" else "❌"}\n")
        }

        // Check battery optimization
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
            val isIgnoringBatteryOptimizations = pm.isIgnoringBatteryOptimizations(packageName)
            sb.append("Battery Opt Exempt: ${if (isIgnoringBatteryOptimizations) "✅" else "❌"}\n")
        }

        // Check display over other apps
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val canDrawOverlays = Settings.canDrawOverlays(this)
            sb.append("Display Over Apps: ${if (canDrawOverlays) "✅" else "❌"}\n")
        }

        sb.append("\nDevice: ${Build.MANUFACTURER} ${Build.MODEL}\n")
        sb.append("Android: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")

        android.app.AlertDialog.Builder(this)
            .setTitle("Debug: All Settings")
            .setMessage(sb.toString())
            .setPositiveButton("OK", null)
            .show()
    }
}
