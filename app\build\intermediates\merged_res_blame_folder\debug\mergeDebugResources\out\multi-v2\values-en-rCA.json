{"logs": [{"outputFile": "com.cyberself.copilot.app-mergeDebugResources-44:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8cb4d84fca6908b8a011123061ad45a8\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,707,8176", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "196,298,397,496,600,702,818,8272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8540,8626", "endColumns": "85,84", "endOffsets": "8621,8706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,75,71,69,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1612"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "823,915,997,1091,1190,1276,1358,7552,7641,7725,7803,7885,7958,8034,8106,8277,8354,8420", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,75,71,69,76,65,119", "endOffsets": "910,992,1086,1185,1271,1353,1443,7636,7720,7798,7880,7953,8029,8101,8171,8349,8415,8535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2467,2588,2704,2827,2952,3044,3142,3259,3383,3480,3582,3684,3814,3953,4059,4158,4234,4330,4424,4528,4615,4702,4804,4884,4968,5069,5170,5270,5369,5457,5563,5664,5768,5884,5964,6064", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2462,2583,2699,2822,2947,3039,3137,3254,3378,3475,3577,3679,3809,3948,4054,4153,4229,4325,4419,4523,4610,4697,4799,4879,4963,5064,5165,5265,5364,5452,5558,5659,5763,5879,5959,6059,6154"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1448,1566,1682,1793,1907,2006,2101,2213,2349,2465,2601,2685,2784,2875,2972,3091,3216,3320,3447,3570,3698,3860,3981,4097,4220,4345,4437,4535,4652,4776,4873,4975,5077,5207,5346,5452,5551,5627,5723,5817,5921,6008,6095,6197,6277,6361,6462,6563,6663,6762,6850,6956,7057,7161,7277,7357,7457", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "1561,1677,1788,1902,2001,2096,2208,2344,2460,2596,2680,2779,2870,2967,3086,3211,3315,3442,3565,3693,3855,3976,4092,4215,4340,4432,4530,4647,4771,4868,4970,5072,5202,5341,5447,5546,5622,5718,5812,5916,6003,6090,6192,6272,6356,6457,6558,6658,6757,6845,6951,7052,7156,7272,7352,7452,7547"}}]}]}