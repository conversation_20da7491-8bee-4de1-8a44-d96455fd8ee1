[{"merged": "com.cyberself.copilot.app-release-42:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/drawable_ic_launcher_background.xml.flat", "source": "com.cyberself.copilot.app-main-43:/drawable/ic_launcher_background.xml"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.cyberself.copilot.app-release-42:/xml_data_extraction_rules.xml.flat", "source": "com.cyberself.copilot.app-main-43:/xml/data_extraction_rules.xml"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/drawable_ic_launcher_foreground.xml.flat", "source": "com.cyberself.copilot.app-main-43:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.cyberself.copilot.app-release-42:/xml_backup_rules.xml.flat", "source": "com.cyberself.copilot.app-main-43:/xml/backup_rules.xml"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.cyberself.copilot.app-release-42:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.cyberself.copilot.app-main-43:/mipmap-xxhdpi/ic_launcher.webp"}]