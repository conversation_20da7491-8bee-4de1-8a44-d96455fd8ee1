<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.VIBRATE"/>
  <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
  <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- For Android 12+ -->
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
  <uses-permission android:name="android.permission.WAKE_LOCK" />
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
  <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
  <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO"/>
  <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.USE_FINGERPRINT"/>
  <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
  <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
  <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
  <uses-permission android:name="com.xiaomi.permission.AUTOSTART" />
  <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/> <!-- Required for Android 13+ -->
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" /> <!-- Android 14+ -->
  <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" /> <!-- For better wake detection -->
  <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- For DND bypass -->

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AlarmTest"
        tools:targetApi="31">
        <!-- Main Activity for Alarm Testing -->
        <activity
            android:name=".TestAlarmActivity"
            android:exported="true"
            android:label="Alarm Test"
            android:theme="@style/Theme.AlarmTest">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Alarm Launcher Activity - Displays when alarm triggers -->
        <activity
            android:name=".AlarmLauncherActivity"
            android:exported="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTop"
            android:showOnLockScreen="true"
            android:turnScreenOn="true"
            android:theme="@style/Theme.AlarmTest" />

        <!-- Alarm Receiver - Handles alarm broadcasts -->
        <receiver
            android:name=".AlarmReceiver"
            android:enabled="true"
            android:exported="true" />

        <!-- Boot Receiver - Restores alarms after device restart -->
        <receiver
            android:name=".BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- Alarm Service - Foreground service for alarm management -->
        <service
            android:name=".AlarmService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="alarm" />
        </service>
    </application>

</manifest>
