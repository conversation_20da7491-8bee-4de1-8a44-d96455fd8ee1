# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.20"
  }
  digests {
    sha256: "\343\230\266ywb\'\030\277\030\377\231\2679\307\331\332\006\0173\373E\212.% 2!\301j\360\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.20"
  }
  digests {
    sha256: "\257\036\304\f;\225\032\375\314\f*\001s\307\270\027c\305(\034-[\257\277\n\205D\242L]\314\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.0"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\317{rd|y\225\a\025\210\376\207\004P\377\234\217\022\177%=-HQ\341a\270\000\366z\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\347a\377oO\a\t5X\267\3157\t(\363\035S\264n\251\024A\365-\3255\332\226\324p\214+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.3"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.3"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.3"
  }
  digests {
    sha256: "\323\372\"W\315L\365\022q}\300\214\0233\210r\2616\271}-Q\017\3167;\234\303\221\352\031\271"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\312\316\377\320v\271\207Ou\352\307 \256H\313.\213\372Uc\023\225\271\177\260\366\202\322y4*\025"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.0"
  }
  digests {
    sha256: "E\334cRLpZ\266\032\265g\223\236\265\v\233\204\005\234P]\ak+\337-\312&hJ\000\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.0"
  }
  digests {
    sha256: "-ggP\3726\371\360$7\005\035=^\345\341`\266q\302\232\251l1\njK\215\307\251\326\226"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\323\365\321B;\306\334\fsC\247#\211B[\344\266\357\203.\364a\372\361T+\274bK\3339\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\273\365M\3140\f\201\352\221\365fz\300\270Y4\262\313\312\377\300\200m\203\211\337\024l\302\024\212\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.3"
  }
  digests {
    sha256: "\001w\226\224\261\350\"\322\356\271\336\316\310o@\334)\203\200k\230_\264\216\341\026\310`SNz\305"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.3"
  }
  digests {
    sha256: "\341{\f\324\000\201\250l\243\255\313\213\023^\350/=\330B\333B\a.\262\220\034d\242w:mu"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.3"
  }
  digests {
    sha256: "\203\235\321$S\304\340Q\026s(1\377I\362\020\211<\311\233p\221\273\232\022I\210\221\034Sw4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\341\377\306_\202S\2552Es\367~:T\321\'ZF\351\177\031\372*\ap\212l\264\265\314N\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\351\265Vc3\214A\247P\003\215\314\317G)\035\226\027\251}-\240\203\377\200@1\331\256G\331I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.0"
  }
  digests {
    sha256: "#w\245\257\262\345\237\206It\355\224\336\371\305\252\204\022\350\bBi\274[\247\027\364\262b\251I\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\023!\245\a\310`_\202\005\r\345\353\256\216\r\a*\271\267\302z\2355^\016>>\2222q\203T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\213!Q{1\022\265\267\2178\322\034X\034\230\033U*\b!\206_\355\234\257s6\ro\217q\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\017\3544z\021o\003\016z\272\213\035A\b8S2\023@\234\222\271 $\253\363_\205V\200M\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\360\244v0\264\340\f\033\231{?\347o\331n \253\354\211\372\325F\321\221c\332\030\324\240\210\241S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.0"
  }
  digests {
    sha256: "c\022v\330\b\303>5\253\362\371\027\300r\372\034\321z\225\222\233\210<t\311B\250\322\2072\233\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.0"
  }
  digests {
    sha256: "C\234\233\310\335\225B\352| \205\031\a{\215G\304\306\215>i\237\216\226\037\270\v\302\225[\273\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "-\302#Y\336\203\355\002Y\250\267\203O\026\331\207\330*\220\307\031~\253\264Mr\313\332\306{\370,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\323\340\371\200\376\231\216\314\333\360\351x\354\346\324)\345\346\255\211\225z2\303\360r8)\356\355\206$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.09.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\363\033\333\202\005\207\300\231\271Fb\346\352\315B\217B\311!\036\326\fE\261\027uc\016\311\356\247\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\222]$c\310\vG\263\360L\253oI8\025>\017]\225\217\020\255\371B(\327\036\340n\037\304\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\247W8\270\3379\204\343\376\036\216I\304\265\373:\002\'\372\006 \323T\016\335\217jZw\231\307\300"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 40
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 18
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 40
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 24
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 3
}
library_dependencies {
  library_index: 26
  library_dep_index: 6
  library_dep_index: 20
  library_dep_index: 20
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 40
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 40
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 16
  library_dep_index: 36
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 27
  library_dep_index: 40
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 32
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 40
}
library_dependencies {
  library_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 40
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 40
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 41
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 42
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 40
  library_dep_index: 38
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 40
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 28
  library_dep_index: 14
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 50
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 48
  library_dep_index: 49
  library_dep_index: 5
  library_dep_index: 36
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 49
  library_dep_index: 47
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 43
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 48
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 52
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 59
  library_dep_index: 55
  library_dep_index: 8
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 30
  library_dep_index: 38
  library_dep_index: 45
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 64
  library_dep_index: 59
  library_dep_index: 55
  library_dep_index: 68
}
library_dependencies {
  library_index: 52
  library_dep_index: 8
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 64
  library_dep_index: 59
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 64
  library_dep_index: 59
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 59
  library_dep_index: 55
  library_dep_index: 8
  library_dep_index: 66
  library_dep_index: 2
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 61
  library_dep_index: 64
  library_dep_index: 59
  library_dep_index: 55
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 32
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 64
  library_dep_index: 55
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 57
  library_dep_index: 59
  library_dep_index: 55
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 22
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 64
  library_dep_index: 59
  library_dep_index: 55
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
  library_dep_index: 32
  library_dep_index: 2
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 59
  library_dep_index: 55
}
library_dependencies {
  library_index: 66
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 67
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 70
  library_dep_index: 74
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 2
  library_dep_index: 74
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 72
  library_dep_index: 74
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 55
  library_dep_index: 2
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 59
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 22
  library_dep_index: 70
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 72
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 59
  library_dep_index: 55
  library_dep_index: 8
  library_dep_index: 2
  library_dep_index: 68
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 64
  library_dep_index: 78
  library_dep_index: 33
  library_dep_index: 51
  library_dep_index: 58
  library_dep_index: 65
  library_dep_index: 72
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 34
  library_dep_index: 53
  library_dep_index: 59
  library_dep_index: 73
  library_dep_index: 69
  library_dep_index: 75
  library_dep_index: 80
  library_dep_index: 82
  library_dep_index: 62
  library_dep_index: 56
  library_dep_index: 54
  library_dep_index: 60
  library_dep_index: 35
  library_dep_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 47
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 72
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 26
  library_dep_index: 2
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 10
  library_dep_index: 70
  library_dep_index: 68
  library_dep_index: 32
  library_dep_index: 55
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 36
  dependency_index: 47
  dependency_index: 76
  dependency_index: 50
  dependency_index: 57
  dependency_index: 64
  dependency_index: 77
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
