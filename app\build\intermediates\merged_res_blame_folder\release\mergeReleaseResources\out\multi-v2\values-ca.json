{"logs": [{"outputFile": "com.cyberself.copilot.app-mergeReleaseResources-40:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1482,1603,1722,1841,1961,2061,2159,2274,2416,2531,2690,2774,2872,2970,3071,3188,3317,3420,3561,3701,3842,4008,4141,4258,4379,4508,4607,4704,4825,4970,5076,5189,5303,5442,5587,5696,5803,5889,5990,6091,6202,6288,6374,6485,6565,6649,6750,6858,6957,7061,7148,7261,7361,7468,7587,7667,7784", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "1598,1717,1836,1956,2056,2154,2269,2411,2526,2685,2769,2867,2965,3066,3183,3312,3415,3556,3696,3837,4003,4136,4253,4374,4503,4602,4699,4820,4965,5071,5184,5298,5437,5582,5691,5798,5884,5985,6086,6197,6283,6369,6480,6560,6644,6745,6853,6952,7056,7143,7256,7356,7463,7582,7662,7779,7886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8cb4d84fca6908b8a011123061ad45a8\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,499,605,710,8550", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "196,298,397,494,600,705,831,8646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1014,1105,1181,1256,1335,1410,1492,1563", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,1009,1100,1176,1251,1330,1405,1487,1558,1678"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,933,1017,1121,1224,1313,1391,7891,7982,8068,8154,8245,8321,8396,8475,8651,8733,8804", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "928,1012,1116,1219,1308,1386,1477,7977,8063,8149,8240,8316,8391,8470,8545,8728,8799,8919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8924,9022", "endColumns": "97,101", "endOffsets": "9017,9119"}}]}]}