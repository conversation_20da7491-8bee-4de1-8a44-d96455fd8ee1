1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cyberself.copilot"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:4:5-66
11-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:5:3-63
12-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:5:20-61
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:6:3-77
13-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:6:20-74
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- For Android 12+ -->
14-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:7:3-72
14-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:7:20-69
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:8:3-75
15-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:8:20-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:9:3-87
16-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:9:20-84
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:10:3-79
17-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:10:20-76
18    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
18-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:11:3-74
18-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:11:20-71
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:12:3-66
19-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:12:20-63
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:13:3-77
20-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:13:20-75
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:14:3-76
21-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:14:20-73
22    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
22-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:15:3-79
22-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:15:20-76
23    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
23-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:16:3-71
23-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:16:20-68
24    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
24-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:17:3-73
24-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:17:20-70
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18:3-77
25-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18:20-75
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:19:3-68
26-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:19:20-66
27    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
27-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:20:3-69
27-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:20:20-67
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21:3-78
28-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21:20-76
29    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
29-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:22:3-71
29-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:22:20-69
30    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
30-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:23:3-93
30-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:23:20-90
31    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
31-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:24:3-95
31-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:24:20-92
32    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
32-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:25:3-73
32-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:25:20-70
33    <uses-permission android:name="com.xiaomi.permission.AUTOSTART" />
33-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:26:3-69
33-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:26:20-66
34    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
34-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:27:3-73
34-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:27:20-70
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:28:3-74
35-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:28:20-72
36
37    <permission
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
38        android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Required for Android 13+ -->
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
42    <application
42-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:30:5-99:19
43        android:allowBackup="true"
43-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:31:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:32:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:33:9-54
49        android:icon="@mipmap/ic_launcher"
49-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:34:9-43
50        android:label="@string/app_name"
50-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:35:9-41
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:36:9-54
52        android:supportsRtl="true"
52-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:37:9-35
53        android:testOnly="true"
54        android:theme="@style/Theme.AlarmTest" >
54-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:38:9-47
55        <activity
55-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:40:9-44:54
56            android:name="com.cyberself.copilot.MainActivity"
56-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:41:13-41
57            android:exported="true"
57-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:42:13-36
58            android:label="@string/app_name"
58-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:43:13-45
59            android:theme="@style/Theme.AlarmTest" />
59-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:44:13-51
60
61        <!-- Test Activity for Alarm Testing -->
62        <activity
62-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:47:9-57:20
63            android:name="com.cyberself.copilot.TestAlarmActivity"
63-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:48:13-46
64            android:exported="true"
64-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:49:13-36
65            android:label="Alarm Test"
65-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:50:13-39
66            android:theme="@style/Theme.AlarmTest" >
66-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:51:13-51
67            <intent-filter>
67-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:52:13-56:29
68                <action android:name="android.intent.action.MAIN" />
68-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:53:17-69
68-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:53:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:55:17-77
70-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:55:27-74
71            </intent-filter>
72        </activity>
73
74        <!-- Alarm Launcher Activity - Displays when alarm triggers -->
75        <activity
75-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:60:9-67:54
76            android:name="com.cyberself.copilot.AlarmLauncherActivity"
76-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:61:13-50
77            android:excludeFromRecents="true"
77-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:63:13-46
78            android:exported="true"
78-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:62:13-36
79            android:launchMode="singleTop"
79-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:64:13-43
80            android:showOnLockScreen="true"
80-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:65:13-44
81            android:theme="@style/Theme.AlarmTest"
81-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:67:13-51
82            android:turnScreenOn="true" />
82-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:66:13-40
83
84        <!-- Alarm Receiver - Handles alarm broadcasts -->
85        <receiver
85-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:70:9-73:39
86            android:name="com.cyberself.copilot.AlarmReceiver"
86-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:71:13-42
87            android:enabled="true"
87-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:72:13-35
88            android:exported="true" />
88-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:73:13-36
89
90        <!-- Boot Receiver - Restores alarms after device restart -->
91        <receiver
91-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:76:9-87:20
92            android:name="com.cyberself.copilot.BootReceiver"
92-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:77:13-41
93            android:enabled="true"
93-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:78:13-35
94            android:exported="true" >
94-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:79:13-36
95            <intent-filter android:priority="1000" >
95-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:80:13-86:29
95-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:80:28-51
96                <action android:name="android.intent.action.BOOT_COMPLETED" />
96-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:81:17-79
96-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:81:25-76
97                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
97-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:82:17-82
97-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:82:25-79
98                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
98-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:83:17-84
98-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:83:25-81
99                <action android:name="android.intent.action.PACKAGE_REPLACED" />
99-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:84:17-81
99-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:84:25-78
100
101                <data android:scheme="package" />
101-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:85:17-50
101-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:85:23-47
102            </intent-filter>
103        </receiver>
104
105        <!-- Alarm Service - Foreground service for alarm management -->
106        <service
106-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:90:9-98:19
107            android:name="com.cyberself.copilot.AlarmService"
107-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:91:13-41
108            android:enabled="true"
108-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:92:13-35
109            android:exported="false"
109-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:93:13-37
110            android:foregroundServiceType="specialUse" >
110-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:94:13-55
111            <property
111-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:95:13-97:41
112                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
112-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:96:17-76
113                android:value="alarm" />
113-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:97:17-38
114        </service>
115
116        <activity
116-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
117            android:name="androidx.compose.ui.tooling.PreviewActivity"
117-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
118            android:exported="true" />
118-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
119        <activity
119-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
120            android:name="androidx.activity.ComponentActivity"
120-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
121            android:exported="true" />
121-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
122
123        <provider
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
124            android:name="androidx.startup.InitializationProvider"
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
125            android:authorities="com.cyberself.copilot.androidx-startup"
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
126            android:exported="false" >
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
127            <meta-data
127-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.emoji2.text.EmojiCompatInitializer"
128-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
129                android:value="androidx.startup" />
129-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
131-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
132                android:value="androidx.startup" />
132-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
135                android:value="androidx.startup" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
136        </provider>
137
138        <receiver
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
139            android:name="androidx.profileinstaller.ProfileInstallReceiver"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
140            android:directBootAware="false"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
141            android:enabled="true"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
142            android:exported="true"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
143            android:permission="android.permission.DUMP" >
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
145                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
146            </intent-filter>
147            <intent-filter>
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
148                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
149            </intent-filter>
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
151                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
152            </intent-filter>
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
154                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
155            </intent-filter>
156        </receiver>
157    </application>
158
159</manifest>
