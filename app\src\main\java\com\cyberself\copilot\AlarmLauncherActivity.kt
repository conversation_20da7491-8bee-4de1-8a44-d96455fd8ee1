package com.cyberself.copilot

import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import android.view.WindowManager
import androidx.activity.ComponentActivity // Import ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.cyberself.copilot.ui.theme.AlarmTestTheme

class AlarmLauncherActivity : ComponentActivity() { // Extend ComponentActivity
    private val TAG = "AlarmLauncherActivity"
    private var wakeLock: PowerManager.WakeLock? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "AlarmLauncherActivity started")

        // Acquire wake lock to ensure screen stays on
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.SCREEN_BRIGHT_WAKE_LOCK or
            PowerManager.ACQUIRE_CAUSES_WAKEUP or
            PowerManager.ON_AFTER_RELEASE,
            "com.cyberself.copilot:AlarmLauncherWakeLock"
        ).apply {
            acquire(30000L) // 30 seconds timeout
        }

        // Set up window to show over lock screen - Enhanced for all Android versions
        setupWindowFlags()

        // Display the alarm UI directly
        val alarmData = intent.getStringExtra("ALARM_DATA")
        Log.d(TAG, "Displaying alarm UI with data: $alarmData")

        setContent {
            AlarmTestTheme {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "🚨 ALARM TRIGGERED! 🚨",
                        fontSize = 32.sp,
                        color = Color.Red,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    Text(
                        text = "Data: $alarmData",
                        fontSize = 16.sp,
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 32.dp)
                    )
                    Button(
                        onClick = { dismissAlarm() },
                        modifier = Modifier.padding(8.dp)
                    ) {
                        Text("DISMISS ALARM", fontSize = 18.sp)
                    }
                    Button(
                        onClick = { snoozeAlarm() },
                        modifier = Modifier.padding(8.dp)
                    ) {
                        Text("SNOOZE (5 min)", fontSize = 18.sp)
                    }
                }
            }
        }
    }

    /**
     * Sets up window flags for universal lock screen bypass and screen wake-up
     * Uses all available flags and multiple strategies for maximum compatibility
     */
    private fun setupWindowFlags() {
        // Use ALL possible window flags for maximum compatibility across devices
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or      // Show over lock screen
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or      // Dismiss lock screen
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or        // Turn screen on
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or        // Keep screen on
            WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON or // Allow lock while on
            WindowManager.LayoutParams.FLAG_FULLSCREEN or            // Full screen mode
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN         // Layout in screen
        )

        // Modern Android API methods (API 27+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)

            // Advanced keyguard dismissal with callbacks
            val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            if (keyguardManager.isKeyguardLocked) {
                keyguardManager.requestDismissKeyguard(this, object : KeyguardManager.KeyguardDismissCallback() {
                    override fun onDismissSucceeded() {
                        Log.d(TAG, "Keyguard dismissed successfully")
                    }
                    override fun onDismissError() {
                        Log.d(TAG, "Keyguard dismiss failed, trying alternative")
                        tryAlternativeLockScreenBypass()
                    }
                    override fun onDismissCancelled() {
                        Log.d(TAG, "Keyguard dismiss cancelled")
                    }
                })
            }
        }

        // Additional enforcement for Android 10+ (API 29+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        }
    }

    private fun tryAlternativeLockScreenBypass() {
        // Alternative method for devices that resist keyguard dismissal
        try {
            // Method 1: Request focus aggressively
            window.decorView.requestFocus()

            // Method 2: Use system UI flags
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                window.decorView.systemUiVisibility = (
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Alternative lock screen bypass failed: ${e.message}")
        }
    }

    private fun dismissAlarm() {
        Log.d(TAG, "Alarm dismissed by user")
        // Release wake lock immediately
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
        finish()
    }

    private fun snoozeAlarm() {
        Log.d(TAG, "Alarm snoozed by user")
        // TODO: Implement snooze functionality - schedule another alarm in 5 minutes
        // For now, just dismiss
        dismissAlarm()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Release wake lock
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
        Log.d(TAG, "AlarmLauncherActivity destroyed")
    }
}
