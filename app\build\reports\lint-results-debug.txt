D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21: Warning: WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the MediaStore.createWriteRequest intent. [ScopedStorage]
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:265: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BatteryLife":
   This issue flags code that either
   * negatively affects battery life, or
   * uses APIs that have recently changed behavior to prevent background tasks
   from consuming memory and battery excessively.

   Generally, you should be using WorkManager instead.

   For more details on how to update your code, please see
   https://developer.android.com/topic/performance/background-optimization


D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:30: Warning: Most apps don't need access to high sensor sampling rate. [HighSamplingRate]
  <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" /> <!-- For better wake detection -->
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HighSamplingRate":
   Most apps don't need access to high sensor sampling rate. Double check your
   use case to ensure your app absolutely needs access to sensor sampling rate
   > 200Hz. Be prepared for your app to be rejected from listing on Play Store
   until your use case for high sensor sampling rate has been reviewed and
   validated by the policy team.

D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:46: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

D:\PROJECTS\alarmtest\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1"
      ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1"
      ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\PROJECTS\alarmtest\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
D:\PROJECTS\alarmtest\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:73: Warning: Exported receiver does not require permission [ExportedReceiver]
        <receiver
         ~~~~~~~~

   Explanation for issues of type "ExportedReceiver":
   Exported receivers (receivers which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the receiver or bind to it.
   Without this, any application can use this receiver.

   https://goo.gle/ExportedReceiver

D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\AlarmLauncherActivity.kt:184: Warning: Wakelocks should be released in onPause, not onDestroy [Wakelock]
                it.release()
                ~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\AlarmReceiver.kt:27: Warning: Should not set both PARTIAL_WAKE_LOCK and ACQUIRE_CAUSES_WAKEUP. If you do not want the screen to turn on, get rid of ACQUIRE_CAUSES_WAKEUP [Wakelock]
                PowerManager.PARTIAL_WAKE_LOCK or
                ^

   Explanation for issues of type "Wakelock":
   Failing to release a wakelock properly can keep the Android device in a
   high power mode, which reduces battery life. There are several causes of
   this, such as releasing the wake lock in onDestroy() instead of in
   onPause(), failing to call release() in all possible code paths after an
   acquire(), and so on.

   NOTE: If you are using the lock just to keep the screen on, you should
   strongly consider using FLAG_KEEP_SCREEN_ON instead. This window flag will
   be correctly managed by the platform as the user moves between applications
   and doesn't require a special permission. See
   https://developer.android.com/reference/android/view/WindowManager.LayoutPa
   rams.html#FLAG_KEEP_SCREEN_ON.

D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\AlarmLauncherActivity.kt:146: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\BootReceiver.kt:74: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        val sharedPrefs = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:180: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) { // setAlarmClock is API 21+
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:261: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:327: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:355: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:364: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:393: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:400: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:266: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("package:$packageName")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:266: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("package:$packageName")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:330: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("package:$packageName")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:330: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                    data = Uri.parse("package:$packageName")
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than
   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:58: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Alarm Test App"
                    ~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:64: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Set Alarm (5 seconds)"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:69: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Set Alarm (30 seconds)"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:74: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Set Alarm (2 minutes)"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:79: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Check Permissions"
                    ~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:84: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Request Battery Opt. Exemption"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:89: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "OEM Settings Guide"
                    ~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:94: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Test Notification Permission"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:99: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Debug: Test Immediate Alarm"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:104: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Debug: Check All Settings"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\PROJECTS\alarmtest\app\src\main\java\com\cyberself\copilot\TestAlarmActivity.kt:109: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            text = "Request Display Over Apps"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

0 errors, 60 warnings
