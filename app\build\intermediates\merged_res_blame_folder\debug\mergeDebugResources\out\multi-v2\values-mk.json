{"logs": [{"outputFile": "com.cyberself.copilot.app-mergeDebugResources-44:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4720,4812,4898,5012,5095,5178,5278,5380,5477,5574,5662,5769,5869,5971,6104,6187,6298", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4715,4807,4893,5007,5090,5173,5273,5375,5472,5569,5657,5764,5864,5966,6099,6182,6293,6396"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1588,1706,1822,1940,2037,2132,2244,2377,2498,2646,2731,2830,2924,3020,3135,3259,3363,3508,3652,3794,3968,4099,4220,4347,4472,4567,4665,4791,4926,5026,5128,5241,5382,5531,5647,5749,5826,5920,6015,6134,6226,6312,6426,6509,6592,6692,6794,6891,6988,7076,7183,7283,7385,7518,7601,7712", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "1583,1701,1817,1935,2032,2127,2239,2372,2493,2641,2726,2825,2919,3015,3130,3254,3358,3503,3647,3789,3963,4094,4215,4342,4467,4562,4660,4786,4921,5021,5123,5236,5377,5526,5642,5744,5821,5915,6010,6129,6221,6307,6421,6504,6587,6687,6789,6886,6983,7071,7178,7278,7380,7513,7596,7707,7810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,1010,1099,1171,1248,1326,1402,1483,1554", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,1005,1094,1166,1243,1321,1397,1478,1549,1670"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,928,1018,1114,1217,1302,1379,7815,7907,7991,8075,8164,8236,8313,8391,8568,8649,8720", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "923,1013,1109,1212,1297,1374,1464,7902,7986,8070,8159,8231,8308,8386,8462,8644,8715,8836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8841,8935", "endColumns": "93,95", "endOffsets": "8930,9026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8cb4d84fca6908b8a011123061ad45a8\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,500,605,708,8467", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "198,300,397,495,600,703,819,8563"}}]}]}