<variant
    name="debug"
    package="com.cyberself.copilot"
    minSdkVersion="24"
    targetSdkVersion="35"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    partialResultsDir="build\intermediates\unit_test_lint_partial_results\debug\lintAnalyzeDebugUnitTest\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b790cd9a42a970f8fbf52b2d10d3871\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\test\AndroidManifest.xml"
        javaDirectories="src\test\java;src\testDebug\java;src\test\kotlin;src\testDebug\kotlin"
        assetsDirectories="src\test\assets;src\testDebug\assets"
        unitTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="UNIT_TEST">
  </artifact>
</variant>
