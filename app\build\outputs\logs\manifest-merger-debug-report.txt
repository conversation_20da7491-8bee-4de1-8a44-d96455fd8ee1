-- Merging decision tree log ---
manifest
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:2:1-104:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f11a7a2dfc998ec016db27aaa3914b46\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff99d65cff2a546440f42c344402c28\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b57b04f4f38d857720215809f490095\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9495e8a9cf20fc000420e72f347dde11\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c6995ad815171efc1ae4866cae8420\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8bfa3ed52d7ab3fc70156eeb45deb1\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\768a0135fad1b68d2ff4600da2618b3b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450ad1f8494baa8712dc8e961b270e76\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc552b859a6823453cf5a19450a4b12e\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70ad4805517d2e95d991e77f8a86f322\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22986f321a1430271ed5fe6e4d778777\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6e61fbb92b07ea0411434dfb83878d9\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91099ce13f35cba9a92cda2d4e372810\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adaffcca5a42f97557ea5314f9d10402\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87e3ebcb0a2193166c53ac89a17bcca5\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e800da8de34149e2d5d16dfe5c62e6bd\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55ead397714428dee272a3ec2a743ee9\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccfacf1a36788aac87e60eb6c10d8cbb\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07f0c92abaf5e3cb600506e85eba9207\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e2f2ba23637fb2fc77b70d9275391b\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a834e761804484e4ec4b1dfddf39cbdf\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b2f1341de8d077ec3300d4fb221a9c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b73c8263989d96cd0697d95ef78ad34\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca48c13147ad5c79d15baecaa240d57\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\308e36532ccdcd283b769249b2840395\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e222d31f5fdb2c221a90702f1bf4127f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0812e207275b95c4055ee4a09cd23f0c\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7b56fd8892f03778482d08ea3c484c5\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e831adfad680e2d45deef6bb48ee204f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0794eece0e9afcd5c26aba9c3358a7\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d830c5d6522c44a74da798561b1b42c1\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e3604db7d5133f7b2d5f5e7e37392c2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f35ba2f851ec6cd72c5f6b46302befd6\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b32c333d7cb32679b5051f8af65b677c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad733498ff2880160f6d594018d14a5f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e21fdb5c475489102a22ac30f6c10b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\437193ea7c137a2ece9418527ca62dda\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\499ae1ce27391d1263ea1d9c521178a0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e05d2ecb29d202b0b0ee91a2614a0907\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:4:5-66
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.VIBRATE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:5:3-63
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:5:20-61
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:6:3-77
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:6:20-74
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:7:3-72
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:7:20-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:8:3-75
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:8:20-72
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:9:3-87
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:9:20-84
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:10:3-79
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:10:20-76
uses-permission#android.permission.QUICKBOOT_POWERON
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:11:3-74
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:11:20-71
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:12:3-66
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:12:20-63
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:13:3-77
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:13:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:14:3-76
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:14:20-73
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:15:3-79
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:15:20-76
uses-permission#android.permission.TURN_SCREEN_ON
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:16:3-71
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:16:20-68
uses-permission#android.permission.SHOW_WHEN_LOCKED
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:17:3-73
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:17:20-70
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18:3-77
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:19:3-68
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:19:20-66
uses-permission#android.permission.USE_BIOMETRIC
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:20:3-69
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:20:20-67
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21:3-78
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21:20-76
uses-permission#android.permission.USE_FINGERPRINT
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:22:3-71
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:22:20-69
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:23:3-93
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:23:20-90
uses-permission#com.huawei.permission.external_app_settings.USE_COMPONENT
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:24:3-95
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:24:20-92
uses-permission#oppo.permission.OPPO_COMPONENT_SAFE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:25:3-73
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:25:20-70
uses-permission#com.xiaomi.permission.AUTOSTART
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:26:3-69
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:26:20-66
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:27:3-73
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:27:20-70
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:28:3-74
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:28:20-72
uses-permission#android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:29:3-91
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:29:20-88
uses-permission#android.permission.HIGH_SAMPLING_RATE_SENSORS
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:30:3-83
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:30:20-80
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:31:3-83
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:31:20-80
application
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:33:5-102:19
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:33:5-102:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e21fdb5c475489102a22ac30f6c10b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e21fdb5c475489102a22ac30f6c10b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:40:9-35
	android:label
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:38:9-41
	android:fullBackupContent
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:36:9-54
	android:roundIcon
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:39:9-54
	tools:targetApi
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:42:9-29
	android:icon
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:37:9-43
	android:allowBackup
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:34:9-35
	android:theme
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:41:9-47
	android:dataExtractionRules
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:35:9-65
activity#com.cyberself.copilot.MainActivity
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:43:9-47:54
	android:label
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:46:13-45
	android:exported
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:45:13-36
	android:theme
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:47:13-51
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:44:13-41
activity#com.cyberself.copilot.TestAlarmActivity
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:50:9-60:20
	android:label
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:53:13-39
	android:exported
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:52:13-36
	android:theme
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:54:13-51
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:51:13-46
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.MAIN
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:58:17-77
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:58:27-74
activity#com.cyberself.copilot.AlarmLauncherActivity
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:63:9-70:54
	android:turnScreenOn
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:69:13-40
	android:excludeFromRecents
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:66:13-46
	android:launchMode
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:67:13-43
	android:exported
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:65:13-36
	android:showOnLockScreen
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:68:13-44
	android:theme
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:70:13-51
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:64:13-50
receiver#com.cyberself.copilot.AlarmReceiver
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:73:9-76:39
	android:enabled
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:75:13-35
	android:exported
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:76:13-36
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:74:13-42
receiver#com.cyberself.copilot.BootReceiver
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:79:9-90:20
	android:enabled
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:81:13-35
	android:exported
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:82:13-36
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:80:13-41
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+data:scheme:package
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:83:13-89:29
	android:priority
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:83:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:84:17-79
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:84:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:85:17-82
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:85:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:86:17-84
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:86:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:87:17-81
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:87:25-78
data
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:88:17-50
	android:scheme
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:88:23-47
service#com.cyberself.copilot.AlarmService
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:93:9-101:19
	android:enabled
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:95:13-35
	android:exported
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:96:13-37
	android:foregroundServiceType
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:97:13-55
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:94:13-41
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:98:13-100:41
	android:value
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:100:17-38
	android:name
		ADDED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:99:17-76
uses-sdk
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f11a7a2dfc998ec016db27aaa3914b46\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f11a7a2dfc998ec016db27aaa3914b46\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff99d65cff2a546440f42c344402c28\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff99d65cff2a546440f42c344402c28\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b57b04f4f38d857720215809f490095\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b57b04f4f38d857720215809f490095\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9495e8a9cf20fc000420e72f347dde11\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9495e8a9cf20fc000420e72f347dde11\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c6995ad815171efc1ae4866cae8420\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c6995ad815171efc1ae4866cae8420\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8bfa3ed52d7ab3fc70156eeb45deb1\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8bfa3ed52d7ab3fc70156eeb45deb1\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\768a0135fad1b68d2ff4600da2618b3b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\768a0135fad1b68d2ff4600da2618b3b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450ad1f8494baa8712dc8e961b270e76\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450ad1f8494baa8712dc8e961b270e76\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc552b859a6823453cf5a19450a4b12e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc552b859a6823453cf5a19450a4b12e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70ad4805517d2e95d991e77f8a86f322\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70ad4805517d2e95d991e77f8a86f322\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22986f321a1430271ed5fe6e4d778777\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22986f321a1430271ed5fe6e4d778777\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6e61fbb92b07ea0411434dfb83878d9\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6e61fbb92b07ea0411434dfb83878d9\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91099ce13f35cba9a92cda2d4e372810\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91099ce13f35cba9a92cda2d4e372810\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adaffcca5a42f97557ea5314f9d10402\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adaffcca5a42f97557ea5314f9d10402\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87e3ebcb0a2193166c53ac89a17bcca5\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87e3ebcb0a2193166c53ac89a17bcca5\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e800da8de34149e2d5d16dfe5c62e6bd\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e800da8de34149e2d5d16dfe5c62e6bd\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55ead397714428dee272a3ec2a743ee9\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55ead397714428dee272a3ec2a743ee9\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccfacf1a36788aac87e60eb6c10d8cbb\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccfacf1a36788aac87e60eb6c10d8cbb\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07f0c92abaf5e3cb600506e85eba9207\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07f0c92abaf5e3cb600506e85eba9207\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e2f2ba23637fb2fc77b70d9275391b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e2f2ba23637fb2fc77b70d9275391b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a834e761804484e4ec4b1dfddf39cbdf\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a834e761804484e4ec4b1dfddf39cbdf\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b2f1341de8d077ec3300d4fb221a9c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b2f1341de8d077ec3300d4fb221a9c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b73c8263989d96cd0697d95ef78ad34\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b73c8263989d96cd0697d95ef78ad34\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca48c13147ad5c79d15baecaa240d57\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca48c13147ad5c79d15baecaa240d57\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\308e36532ccdcd283b769249b2840395\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\308e36532ccdcd283b769249b2840395\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e222d31f5fdb2c221a90702f1bf4127f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e222d31f5fdb2c221a90702f1bf4127f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0812e207275b95c4055ee4a09cd23f0c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0812e207275b95c4055ee4a09cd23f0c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7b56fd8892f03778482d08ea3c484c5\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7b56fd8892f03778482d08ea3c484c5\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e831adfad680e2d45deef6bb48ee204f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e831adfad680e2d45deef6bb48ee204f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0794eece0e9afcd5c26aba9c3358a7\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0794eece0e9afcd5c26aba9c3358a7\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d830c5d6522c44a74da798561b1b42c1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d830c5d6522c44a74da798561b1b42c1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e3604db7d5133f7b2d5f5e7e37392c2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e3604db7d5133f7b2d5f5e7e37392c2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f35ba2f851ec6cd72c5f6b46302befd6\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f35ba2f851ec6cd72c5f6b46302befd6\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b32c333d7cb32679b5051f8af65b677c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b32c333d7cb32679b5051f8af65b677c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad733498ff2880160f6d594018d14a5f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad733498ff2880160f6d594018d14a5f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e21fdb5c475489102a22ac30f6c10b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e21fdb5c475489102a22ac30f6c10b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\437193ea7c137a2ece9418527ca62dda\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\437193ea7c137a2ece9418527ca62dda\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\499ae1ce27391d1263ea1d9c521178a0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\499ae1ce27391d1263ea1d9c521178a0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e05d2ecb29d202b0b0ee91a2614a0907\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e05d2ecb29d202b0b0ee91a2614a0907\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c077c7fa91fcc97275a88ea73d7d855\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
