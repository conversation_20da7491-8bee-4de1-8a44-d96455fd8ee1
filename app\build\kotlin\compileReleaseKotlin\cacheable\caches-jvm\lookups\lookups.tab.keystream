  Manifest android  POST_NOTIFICATIONS android.Manifest.permission  ic_lock_idle_alarm android.R.drawable  Activity android.app  AlarmLauncherActivity android.app  AlarmManager android.app  
AlarmReceiver android.app  Build android.app  Color android.app  Context android.app  	Exception android.app  Handler android.app  IBinder android.app  Int android.app  Intent android.app  KeyguardManager android.app  List android.app  Log android.app  Long android.app  Looper android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationManager android.app  Pair android.app  
PendingIntent android.app  START_NOT_STICKY android.app  START_STICKY android.app  Service android.app  String android.app  System android.app  Thread android.app  android android.app  apply android.app  java android.app  let android.app  
mutableListOf android.app  split android.app  
startsWith android.app  toLong android.app  ActivityCompat android.app.Activity  AlarmLauncherActivity android.app.Activity  AlarmManager android.app.Activity  
AlarmReceiver android.app.Activity  AlarmTestTheme android.app.Activity  	Alignment android.app.Activity  Arrangement android.app.Activity  Build android.app.Activity  Button android.app.Activity  Color android.app.Activity  Column android.app.Activity  Context android.app.Activity  
ContextCompat android.app.Activity  	Exception android.app.Activity  Greeting android.app.Activity  Intent android.app.Activity  KeyguardManager android.app.Activity  LinearLayout android.app.Activity  Log android.app.Activity  Manifest android.app.Activity  Modifier android.app.Activity  NotificationChannel android.app.Activity  NotificationCompat android.app.Activity  NotificationManager android.app.Activity  PackageManager android.app.Activity  
PendingIntent android.app.Activity  PowerManager android.app.Activity  Scaffold android.app.Activity  Settings android.app.Activity  System android.app.Activity  Text android.app.Activity  	TextAlign android.app.Activity  TextView android.app.Activity  Toast android.app.Activity  Uri android.app.Activity  
WindowManager android.app.Activity  android android.app.Activity  apply android.app.Activity  arrayOf android.app.Activity  checkPermissions android.app.Activity  contains android.app.Activity  dismissAlarm android.app.Activity  dp android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  getSystemService android.app.Activity  intent android.app.Activity  
isNotEmpty android.app.Activity  java android.app.Activity  let android.app.Activity  	lowercase android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onRequestPermissionsResult android.app.Activity  padding android.app.Activity  #requestBatteryOptimizationExemption android.app.Activity  
setContent android.app.Activity  setContentView android.app.Activity  setShowWhenLocked android.app.Activity  setTestAlarm android.app.Activity  setTurnScreenOn android.app.Activity  showOEMSettingsGuide android.app.Activity  snoozeAlarm android.app.Activity  sp android.app.Activity  
startActivity android.app.Activity  testNotificationPermission android.app.Activity  window android.app.Activity  AlarmClockInfo android.app.AlarmManager  
RTC_WAKEUP android.app.AlarmManager  canScheduleExactAlarms android.app.AlarmManager  
setAlarmClock android.app.AlarmManager  setExactAndAllowWhileIdle android.app.AlarmManager  Builder android.app.AlertDialog  
setMessage android.app.AlertDialog.Builder  setPositiveButton android.app.AlertDialog.Builder  setTitle android.app.AlertDialog.Builder  show android.app.AlertDialog.Builder  requestDismissKeyguard android.app.KeyguardManager  FLAG_INSISTENT android.app.Notification  VISIBILITY_PUBLIC android.app.Notification  flags android.app.Notification  Color android.app.NotificationChannel  Notification android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  
lightColor android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  setBypassDnd android.app.NotificationChannel  setSound android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  getBroadcast android.app.PendingIntent  AlarmLauncherActivity android.app.Service  AlarmManager android.app.Service  
AlarmReceiver android.app.Service  Build android.app.Service  Color android.app.Service  Context android.app.Service  	Exception android.app.Service  Handler android.app.Service  Intent android.app.Service  Log android.app.Service  Long android.app.Service  Looper android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  Pair android.app.Service  
PendingIntent android.app.Service  START_NOT_STICKY android.app.Service  START_STICKY android.app.Service  String android.app.Service  System android.app.Service  Thread android.app.Service  android android.app.Service  apply android.app.Service  java android.app.Service  let android.app.Service  
mutableListOf android.app.Service  onCreate android.app.Service  split android.app.Service  startForeground android.app.Service  
startsWith android.app.Service  stopSelf android.app.Service  toLong android.app.Service  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  ALARM_NOTIFICATION_ID !android.content.BroadcastReceiver  AlarmLauncherActivity !android.content.BroadcastReceiver  AlarmManager !android.content.BroadcastReceiver  
AlarmReceiver !android.content.BroadcastReceiver  AlarmService !android.content.BroadcastReceiver  Build !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Handler !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  Long !android.content.BroadcastReceiver  Looper !android.content.BroadcastReceiver  Notification !android.content.BroadcastReceiver  NotificationChannel !android.content.BroadcastReceiver  NotificationCompat !android.content.BroadcastReceiver  NotificationManager !android.content.BroadcastReceiver  Pair !android.content.BroadcastReceiver  
PendingIntent !android.content.BroadcastReceiver  PowerManager !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  System !android.content.BroadcastReceiver  android !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  let !android.content.BroadcastReceiver  
mutableListOf !android.content.BroadcastReceiver  run !android.content.BroadcastReceiver  split !android.content.BroadcastReceiver  
startsWith !android.content.BroadcastReceiver  toLong !android.content.BroadcastReceiver  
ALARM_SERVICE android.content.Context  ActivityCompat android.content.Context  AlarmLauncherActivity android.content.Context  AlarmManager android.content.Context  
AlarmReceiver android.content.Context  AlarmTestTheme android.content.Context  	Alignment android.content.Context  Arrangement android.content.Context  Build android.content.Context  Button android.content.Context  Color android.content.Context  Column android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  	Exception android.content.Context  Greeting android.content.Context  Handler android.content.Context  Intent android.content.Context  KEYGUARD_SERVICE android.content.Context  KeyguardManager android.content.Context  LinearLayout android.content.Context  Log android.content.Context  Long android.content.Context  Looper android.content.Context  MODE_PRIVATE android.content.Context  Manifest android.content.Context  Modifier android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  
POWER_SERVICE android.content.Context  PackageManager android.content.Context  Pair android.content.Context  
PendingIntent android.content.Context  PowerManager android.content.Context  START_NOT_STICKY android.content.Context  START_STICKY android.content.Context  Scaffold android.content.Context  Settings android.content.Context  String android.content.Context  System android.content.Context  Text android.content.Context  	TextAlign android.content.Context  TextView android.content.Context  Thread android.content.Context  Toast android.content.Context  Uri android.content.Context  
WindowManager android.content.Context  android android.content.Context  apply android.content.Context  arrayOf android.content.Context  checkPermissions android.content.Context  contains android.content.Context  #createDeviceProtectedStorageContext android.content.Context  dismissAlarm android.content.Context  dp android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  
isNotEmpty android.content.Context  java android.content.Context  let android.content.Context  	lowercase android.content.Context  
mutableListOf android.content.Context  padding android.content.Context  #requestBatteryOptimizationExemption android.content.Context  
setContent android.content.Context  setTestAlarm android.content.Context  showOEMSettingsGuide android.content.Context  snoozeAlarm android.content.Context  sp android.content.Context  split android.content.Context  
startActivity android.content.Context  startForegroundService android.content.Context  startService android.content.Context  
startsWith android.content.Context  testNotificationPermission android.content.Context  toLong android.content.Context  ActivityCompat android.content.ContextWrapper  AlarmLauncherActivity android.content.ContextWrapper  AlarmManager android.content.ContextWrapper  
AlarmReceiver android.content.ContextWrapper  AlarmTestTheme android.content.ContextWrapper  	Alignment android.content.ContextWrapper  Arrangement android.content.ContextWrapper  Build android.content.ContextWrapper  Button android.content.ContextWrapper  Color android.content.ContextWrapper  Column android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  	Exception android.content.ContextWrapper  Greeting android.content.ContextWrapper  Handler android.content.ContextWrapper  Intent android.content.ContextWrapper  KeyguardManager android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  Looper android.content.ContextWrapper  Manifest android.content.ContextWrapper  Modifier android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  PackageManager android.content.ContextWrapper  Pair android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PowerManager android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  Scaffold android.content.ContextWrapper  Settings android.content.ContextWrapper  String android.content.ContextWrapper  System android.content.ContextWrapper  Text android.content.ContextWrapper  	TextAlign android.content.ContextWrapper  TextView android.content.ContextWrapper  Thread android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  arrayOf android.content.ContextWrapper  checkPermissions android.content.ContextWrapper  contains android.content.ContextWrapper  dismissAlarm android.content.ContextWrapper  dp android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  getSystemService android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  java android.content.ContextWrapper  let android.content.ContextWrapper  	lowercase android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  packageName android.content.ContextWrapper  padding android.content.ContextWrapper  #requestBatteryOptimizationExemption android.content.ContextWrapper  
setContent android.content.ContextWrapper  setTestAlarm android.content.ContextWrapper  showOEMSettingsGuide android.content.ContextWrapper  snoozeAlarm android.content.ContextWrapper  sp android.content.ContextWrapper  split android.content.ContextWrapper  
startActivity android.content.ContextWrapper  
startsWith android.content.ContextWrapper  testNotificationPermission android.content.ContextWrapper  toLong android.content.ContextWrapper  ACTION_BOOT_COMPLETED android.content.Intent  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  "FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_ACTIVITY_NO_HISTORY android.content.Intent  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  Intent android.content.Intent  Uri android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  data android.content.Intent  flags android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  all !android.content.SharedPreferences  	getString !android.content.SharedPreferences  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Color android.graphics  RED android.graphics.Color  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  MANUFACTURER android.os.Build  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  O_MR1 android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  postDelayed android.os.Handler  
getMainLooper android.os.Looper  ACQUIRE_CAUSES_WAKEUP android.os.PowerManager  ON_AFTER_RELEASE android.os.PowerManager  PARTIAL_WAKE_LOCK android.os.PowerManager  PowerManager android.os.PowerManager  SCREEN_BRIGHT_WAKE_LOCK android.os.PowerManager  WakeLock android.os.PowerManager  apply android.os.PowerManager  isIgnoringBatteryOptimizations android.os.PowerManager  newWakeLock android.os.PowerManager  run android.os.PowerManager  acquire  android.os.PowerManager.WakeLock  isHeld  android.os.PowerManager.WakeLock  let  android.os.PowerManager.WakeLock  release  android.os.PowerManager.WakeLock  Settings android.provider  +ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS android.provider.Settings  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  
WindowManager android.view  ActivityCompat  android.view.ContextThemeWrapper  AlarmLauncherActivity  android.view.ContextThemeWrapper  AlarmManager  android.view.ContextThemeWrapper  
AlarmReceiver  android.view.ContextThemeWrapper  AlarmTestTheme  android.view.ContextThemeWrapper  	Alignment  android.view.ContextThemeWrapper  Arrangement  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  Color  android.view.ContextThemeWrapper  Column  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Greeting  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  KeyguardManager  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  NotificationChannel  android.view.ContextThemeWrapper  NotificationCompat  android.view.ContextThemeWrapper  NotificationManager  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  
PendingIntent  android.view.ContextThemeWrapper  PowerManager  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  	TextAlign  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  checkPermissions  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  dismissAlarm  android.view.ContextThemeWrapper  dp  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  	lowercase  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  #requestBatteryOptimizationExemption  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setTestAlarm  android.view.ContextThemeWrapper  showOEMSettingsGuide  android.view.ContextThemeWrapper  snoozeAlarm  android.view.ContextThemeWrapper  sp  android.view.ContextThemeWrapper  testNotificationPermission  android.view.ContextThemeWrapper  OnClickListener android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  addView android.view.ViewGroup  addFlags android.view.Window  FLAG_DISMISS_KEYGUARD 'android.view.WindowManager.LayoutParams  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  FLAG_SHOW_WHEN_LOCKED 'android.view.WindowManager.LayoutParams  FLAG_TURN_SCREEN_ON 'android.view.WindowManager.LayoutParams  Button android.widget  LinearLayout android.widget  TextView android.widget  Toast android.widget  apply android.widget.Button  checkPermissions android.widget.Button  #requestBatteryOptimizationExemption android.widget.Button  setOnClickListener android.widget.Button  setTestAlarm android.widget.Button  showOEMSettingsGuide android.widget.Button  testNotificationPermission android.widget.Button  text android.widget.Button  LinearLayout android.widget.LinearLayout  VERTICAL android.widget.LinearLayout  addView android.widget.LinearLayout  apply android.widget.LinearLayout  orientation android.widget.LinearLayout  
setPadding android.widget.LinearLayout  apply android.widget.TextView  
setPadding android.widget.TextView  text android.widget.TextView  textSize android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AlarmTestTheme #androidx.activity.ComponentActivity  	Alignment #androidx.activity.ComponentActivity  Arrangement #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  Color #androidx.activity.ComponentActivity  Column #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  Greeting #androidx.activity.ComponentActivity  KeyguardManager #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  PowerManager #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  	TextAlign #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  dismissAlarm #androidx.activity.ComponentActivity  dp #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  snoozeAlarm #androidx.activity.ComponentActivity  sp #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Button .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  dismissAlarm .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  snoozeAlarm .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  Text +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  Button androidx.compose.material3  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  AlarmTestTheme #androidx.core.app.ComponentActivity  	Alignment #androidx.core.app.ComponentActivity  Arrangement #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  Color #androidx.core.app.ComponentActivity  Column #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  Greeting #androidx.core.app.ComponentActivity  KeyguardManager #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  PowerManager #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  	TextAlign #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  dismissAlarm #androidx.core.app.ComponentActivity  dp #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  snoozeAlarm #androidx.core.app.ComponentActivity  sp #androidx.core.app.ComponentActivity  WakeLock 0androidx.core.app.ComponentActivity.PowerManager  Builder $androidx.core.app.NotificationCompat  CATEGORY_ALARM $androidx.core.app.NotificationCompat  DEFAULT_ALL $androidx.core.app.NotificationCompat  PRIORITY_MAX $androidx.core.app.NotificationCompat  VISIBILITY_PUBLIC $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setCategory ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setDefaults ,androidx.core.app.NotificationCompat.Builder  setFullScreenIntent ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
setVisibility ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ALARM_NOTIFICATION_ID com.cyberself.copilot  Activity com.cyberself.copilot  ActivityCompat com.cyberself.copilot  AlarmLauncherActivity com.cyberself.copilot  AlarmManager com.cyberself.copilot  
AlarmReceiver com.cyberself.copilot  AlarmService com.cyberself.copilot  AlarmTestTheme com.cyberself.copilot  	Alignment com.cyberself.copilot  Arrangement com.cyberself.copilot  Array com.cyberself.copilot  BootReceiver com.cyberself.copilot  BroadcastReceiver com.cyberself.copilot  Build com.cyberself.copilot  Bundle com.cyberself.copilot  Button com.cyberself.copilot  Color com.cyberself.copilot  Column com.cyberself.copilot  ComponentActivity com.cyberself.copilot  
Composable com.cyberself.copilot  Context com.cyberself.copilot  
ContextCompat com.cyberself.copilot  	Exception com.cyberself.copilot  Greeting com.cyberself.copilot  GreetingPreview com.cyberself.copilot  Handler com.cyberself.copilot  IBinder com.cyberself.copilot  Int com.cyberself.copilot  IntArray com.cyberself.copilot  Intent com.cyberself.copilot  KeyguardManager com.cyberself.copilot  LinearLayout com.cyberself.copilot  List com.cyberself.copilot  Log com.cyberself.copilot  Long com.cyberself.copilot  Looper com.cyberself.copilot  MainActivity com.cyberself.copilot  Manifest com.cyberself.copilot  Modifier com.cyberself.copilot  Notification com.cyberself.copilot  NotificationChannel com.cyberself.copilot  NotificationCompat com.cyberself.copilot  NotificationManager com.cyberself.copilot  PackageManager com.cyberself.copilot  Pair com.cyberself.copilot  
PendingIntent com.cyberself.copilot  PowerManager com.cyberself.copilot  Preview com.cyberself.copilot  START_NOT_STICKY com.cyberself.copilot  START_STICKY com.cyberself.copilot  Scaffold com.cyberself.copilot  Service com.cyberself.copilot  Settings com.cyberself.copilot  String com.cyberself.copilot  System com.cyberself.copilot  TestAlarmActivity com.cyberself.copilot  Text com.cyberself.copilot  	TextAlign com.cyberself.copilot  TextView com.cyberself.copilot  Thread com.cyberself.copilot  Toast com.cyberself.copilot  Uri com.cyberself.copilot  
WindowManager com.cyberself.copilot  android com.cyberself.copilot  apply com.cyberself.copilot  arrayOf com.cyberself.copilot  checkPermissions com.cyberself.copilot  contains com.cyberself.copilot  dismissAlarm com.cyberself.copilot  fillMaxSize com.cyberself.copilot  
isNotEmpty com.cyberself.copilot  java com.cyberself.copilot  let com.cyberself.copilot  	lowercase com.cyberself.copilot  
mutableListOf com.cyberself.copilot  padding com.cyberself.copilot  #requestBatteryOptimizationExemption com.cyberself.copilot  run com.cyberself.copilot  setTestAlarm com.cyberself.copilot  showOEMSettingsGuide com.cyberself.copilot  snoozeAlarm com.cyberself.copilot  split com.cyberself.copilot  
startsWith com.cyberself.copilot  testNotificationPermission com.cyberself.copilot  toLong com.cyberself.copilot  AlarmTestTheme +com.cyberself.copilot.AlarmLauncherActivity  	Alignment +com.cyberself.copilot.AlarmLauncherActivity  Arrangement +com.cyberself.copilot.AlarmLauncherActivity  Build +com.cyberself.copilot.AlarmLauncherActivity  Button +com.cyberself.copilot.AlarmLauncherActivity  Color +com.cyberself.copilot.AlarmLauncherActivity  Column +com.cyberself.copilot.AlarmLauncherActivity  Context +com.cyberself.copilot.AlarmLauncherActivity  Log +com.cyberself.copilot.AlarmLauncherActivity  Modifier +com.cyberself.copilot.AlarmLauncherActivity  PowerManager +com.cyberself.copilot.AlarmLauncherActivity  TAG +com.cyberself.copilot.AlarmLauncherActivity  Text +com.cyberself.copilot.AlarmLauncherActivity  	TextAlign +com.cyberself.copilot.AlarmLauncherActivity  
WindowManager +com.cyberself.copilot.AlarmLauncherActivity  apply +com.cyberself.copilot.AlarmLauncherActivity  dismissAlarm +com.cyberself.copilot.AlarmLauncherActivity  dp +com.cyberself.copilot.AlarmLauncherActivity  fillMaxSize +com.cyberself.copilot.AlarmLauncherActivity  finish +com.cyberself.copilot.AlarmLauncherActivity  getSystemService +com.cyberself.copilot.AlarmLauncherActivity  intent +com.cyberself.copilot.AlarmLauncherActivity  let +com.cyberself.copilot.AlarmLauncherActivity  padding +com.cyberself.copilot.AlarmLauncherActivity  
setContent +com.cyberself.copilot.AlarmLauncherActivity  setShowWhenLocked +com.cyberself.copilot.AlarmLauncherActivity  setTurnScreenOn +com.cyberself.copilot.AlarmLauncherActivity  setupWindowFlags +com.cyberself.copilot.AlarmLauncherActivity  snoozeAlarm +com.cyberself.copilot.AlarmLauncherActivity  sp +com.cyberself.copilot.AlarmLauncherActivity  wakeLock +com.cyberself.copilot.AlarmLauncherActivity  window +com.cyberself.copilot.AlarmLauncherActivity  ALARM_NOTIFICATION_ID #com.cyberself.copilot.AlarmReceiver  AlarmLauncherActivity #com.cyberself.copilot.AlarmReceiver  AlarmService #com.cyberself.copilot.AlarmReceiver  Build #com.cyberself.copilot.AlarmReceiver  	Companion #com.cyberself.copilot.AlarmReceiver  Context #com.cyberself.copilot.AlarmReceiver  	Exception #com.cyberself.copilot.AlarmReceiver  Handler #com.cyberself.copilot.AlarmReceiver  Intent #com.cyberself.copilot.AlarmReceiver  Log #com.cyberself.copilot.AlarmReceiver  Looper #com.cyberself.copilot.AlarmReceiver  Notification #com.cyberself.copilot.AlarmReceiver  NotificationChannel #com.cyberself.copilot.AlarmReceiver  NotificationCompat #com.cyberself.copilot.AlarmReceiver  NotificationManager #com.cyberself.copilot.AlarmReceiver  
PendingIntent #com.cyberself.copilot.AlarmReceiver  PowerManager #com.cyberself.copilot.AlarmReceiver  String #com.cyberself.copilot.AlarmReceiver  System #com.cyberself.copilot.AlarmReceiver  TAG #com.cyberself.copilot.AlarmReceiver  android #com.cyberself.copilot.AlarmReceiver  apply #com.cyberself.copilot.AlarmReceiver  java #com.cyberself.copilot.AlarmReceiver  run #com.cyberself.copilot.AlarmReceiver  showFullScreenNotification #com.cyberself.copilot.AlarmReceiver  startAlarmService #com.cyberself.copilot.AlarmReceiver  tryDirectActivityLaunch #com.cyberself.copilot.AlarmReceiver  ALARM_NOTIFICATION_ID -com.cyberself.copilot.AlarmReceiver.Companion  AlarmLauncherActivity -com.cyberself.copilot.AlarmReceiver.Companion  AlarmService -com.cyberself.copilot.AlarmReceiver.Companion  Build -com.cyberself.copilot.AlarmReceiver.Companion  Context -com.cyberself.copilot.AlarmReceiver.Companion  Handler -com.cyberself.copilot.AlarmReceiver.Companion  Intent -com.cyberself.copilot.AlarmReceiver.Companion  Log -com.cyberself.copilot.AlarmReceiver.Companion  Looper -com.cyberself.copilot.AlarmReceiver.Companion  Notification -com.cyberself.copilot.AlarmReceiver.Companion  NotificationChannel -com.cyberself.copilot.AlarmReceiver.Companion  NotificationCompat -com.cyberself.copilot.AlarmReceiver.Companion  NotificationManager -com.cyberself.copilot.AlarmReceiver.Companion  
PendingIntent -com.cyberself.copilot.AlarmReceiver.Companion  PowerManager -com.cyberself.copilot.AlarmReceiver.Companion  System -com.cyberself.copilot.AlarmReceiver.Companion  android -com.cyberself.copilot.AlarmReceiver.Companion  apply -com.cyberself.copilot.AlarmReceiver.Companion  java -com.cyberself.copilot.AlarmReceiver.Companion  run -com.cyberself.copilot.AlarmReceiver.Companion  AlarmLauncherActivity "com.cyberself.copilot.AlarmService  AlarmManager "com.cyberself.copilot.AlarmService  
AlarmReceiver "com.cyberself.copilot.AlarmService  Build "com.cyberself.copilot.AlarmService  
CHANNEL_ID "com.cyberself.copilot.AlarmService  Color "com.cyberself.copilot.AlarmService  Context "com.cyberself.copilot.AlarmService  Handler "com.cyberself.copilot.AlarmService  Intent "com.cyberself.copilot.AlarmService  Log "com.cyberself.copilot.AlarmService  Looper "com.cyberself.copilot.AlarmService  NOTIFICATION_ID "com.cyberself.copilot.AlarmService  NotificationChannel "com.cyberself.copilot.AlarmService  NotificationCompat "com.cyberself.copilot.AlarmService  NotificationManager "com.cyberself.copilot.AlarmService  Pair "com.cyberself.copilot.AlarmService  
PendingIntent "com.cyberself.copilot.AlarmService  START_NOT_STICKY "com.cyberself.copilot.AlarmService  START_STICKY "com.cyberself.copilot.AlarmService  System "com.cyberself.copilot.AlarmService  TAG "com.cyberself.copilot.AlarmService  Thread "com.cyberself.copilot.AlarmService  android "com.cyberself.copilot.AlarmService  apply "com.cyberself.copilot.AlarmService  createAlarmNotification "com.cyberself.copilot.AlarmService  createNotification "com.cyberself.copilot.AlarmService  createNotificationChannel "com.cyberself.copilot.AlarmService  getAlarmsFromPrefs "com.cyberself.copilot.AlarmService  getSharedPreferences "com.cyberself.copilot.AlarmService  getSystemService "com.cyberself.copilot.AlarmService  java "com.cyberself.copilot.AlarmService  launchAlarmActivity "com.cyberself.copilot.AlarmService  let "com.cyberself.copilot.AlarmService  
mutableListOf "com.cyberself.copilot.AlarmService  
restoreAlarms "com.cyberself.copilot.AlarmService  split "com.cyberself.copilot.AlarmService  
startActivity "com.cyberself.copilot.AlarmService  startForeground "com.cyberself.copilot.AlarmService  
startsWith "com.cyberself.copilot.AlarmService  stopSelf "com.cyberself.copilot.AlarmService  toLong "com.cyberself.copilot.AlarmService  AlarmManager "com.cyberself.copilot.BootReceiver  
AlarmReceiver "com.cyberself.copilot.BootReceiver  Build "com.cyberself.copilot.BootReceiver  Context "com.cyberself.copilot.BootReceiver  Intent "com.cyberself.copilot.BootReceiver  Log "com.cyberself.copilot.BootReceiver  Pair "com.cyberself.copilot.BootReceiver  
PendingIntent "com.cyberself.copilot.BootReceiver  System "com.cyberself.copilot.BootReceiver  TAG "com.cyberself.copilot.BootReceiver  apply "com.cyberself.copilot.BootReceiver  getAlarmsFromPrefs "com.cyberself.copilot.BootReceiver  java "com.cyberself.copilot.BootReceiver  let "com.cyberself.copilot.BootReceiver  
mutableListOf "com.cyberself.copilot.BootReceiver  split "com.cyberself.copilot.BootReceiver  
startsWith "com.cyberself.copilot.BootReceiver  toLong "com.cyberself.copilot.BootReceiver  AlarmTestTheme "com.cyberself.copilot.MainActivity  Greeting "com.cyberself.copilot.MainActivity  Modifier "com.cyberself.copilot.MainActivity  Scaffold "com.cyberself.copilot.MainActivity  enableEdgeToEdge "com.cyberself.copilot.MainActivity  fillMaxSize "com.cyberself.copilot.MainActivity  padding "com.cyberself.copilot.MainActivity  
setContent "com.cyberself.copilot.MainActivity  WakeLock "com.cyberself.copilot.PowerManager  ALARM_CHANNEL_ID 'com.cyberself.copilot.TestAlarmActivity  ActivityCompat 'com.cyberself.copilot.TestAlarmActivity  AlarmLauncherActivity 'com.cyberself.copilot.TestAlarmActivity  AlarmManager 'com.cyberself.copilot.TestAlarmActivity  
AlarmReceiver 'com.cyberself.copilot.TestAlarmActivity  Build 'com.cyberself.copilot.TestAlarmActivity  Button 'com.cyberself.copilot.TestAlarmActivity  Context 'com.cyberself.copilot.TestAlarmActivity  
ContextCompat 'com.cyberself.copilot.TestAlarmActivity  Intent 'com.cyberself.copilot.TestAlarmActivity  LinearLayout 'com.cyberself.copilot.TestAlarmActivity  Log 'com.cyberself.copilot.TestAlarmActivity  Manifest 'com.cyberself.copilot.TestAlarmActivity  $NOTIFICATION_PERMISSION_REQUEST_CODE 'com.cyberself.copilot.TestAlarmActivity  NotificationChannel 'com.cyberself.copilot.TestAlarmActivity  NotificationCompat 'com.cyberself.copilot.TestAlarmActivity  NotificationManager 'com.cyberself.copilot.TestAlarmActivity  PackageManager 'com.cyberself.copilot.TestAlarmActivity  
PendingIntent 'com.cyberself.copilot.TestAlarmActivity  Settings 'com.cyberself.copilot.TestAlarmActivity  System 'com.cyberself.copilot.TestAlarmActivity  TAG 'com.cyberself.copilot.TestAlarmActivity  TextView 'com.cyberself.copilot.TestAlarmActivity  Toast 'com.cyberself.copilot.TestAlarmActivity  Uri 'com.cyberself.copilot.TestAlarmActivity  android 'com.cyberself.copilot.TestAlarmActivity  apply 'com.cyberself.copilot.TestAlarmActivity  arrayOf 'com.cyberself.copilot.TestAlarmActivity  checkPermissions 'com.cyberself.copilot.TestAlarmActivity  contains 'com.cyberself.copilot.TestAlarmActivity  createAlarmNotification 'com.cyberself.copilot.TestAlarmActivity  createAlarmNotificationChannel 'com.cyberself.copilot.TestAlarmActivity  getSystemService 'com.cyberself.copilot.TestAlarmActivity  
isNotEmpty 'com.cyberself.copilot.TestAlarmActivity  java 'com.cyberself.copilot.TestAlarmActivity  	lowercase 'com.cyberself.copilot.TestAlarmActivity  packageName 'com.cyberself.copilot.TestAlarmActivity  #requestBatteryOptimizationExemption 'com.cyberself.copilot.TestAlarmActivity  setContentView 'com.cyberself.copilot.TestAlarmActivity  setTestAlarm 'com.cyberself.copilot.TestAlarmActivity  showOEMSettingsGuide 'com.cyberself.copilot.TestAlarmActivity  
startActivity 'com.cyberself.copilot.TestAlarmActivity  testNotificationPermission 'com.cyberself.copilot.TestAlarmActivity  AlarmTestTheme com.cyberself.copilot.ui.theme  Boolean com.cyberself.copilot.ui.theme  Build com.cyberself.copilot.ui.theme  
Composable com.cyberself.copilot.ui.theme  DarkColorScheme com.cyberself.copilot.ui.theme  
FontFamily com.cyberself.copilot.ui.theme  
FontWeight com.cyberself.copilot.ui.theme  LightColorScheme com.cyberself.copilot.ui.theme  Pink40 com.cyberself.copilot.ui.theme  Pink80 com.cyberself.copilot.ui.theme  Purple40 com.cyberself.copilot.ui.theme  Purple80 com.cyberself.copilot.ui.theme  PurpleGrey40 com.cyberself.copilot.ui.theme  PurpleGrey80 com.cyberself.copilot.ui.theme  
Typography com.cyberself.copilot.ui.theme  Unit com.cyberself.copilot.ui.theme  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  Thread 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  start java.lang.Thread  AlarmManager 	java.util  
AlarmReceiver 	java.util  BroadcastReceiver 	java.util  Build 	java.util  Context 	java.util  Date 	java.util  	Exception 	java.util  Intent 	java.util  List 	java.util  Log 	java.util  Long 	java.util  Pair 	java.util  
PendingIntent 	java.util  String 	java.util  System 	java.util  apply 	java.util  java 	java.util  let 	java.util  
mutableListOf 	java.util  split 	java.util  
startsWith 	java.util  toLong 	java.util  Array kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  apply kotlin  arrayOf kotlin  let kotlin  run kotlin  hashCode 
kotlin.Any  not kotlin.Boolean  sp 
kotlin.Double  	compareTo 
kotlin.Int  inc 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  get kotlin.IntArray  
isNotEmpty kotlin.IntArray  	compareTo kotlin.Long  hashCode kotlin.Long  plus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  contains 
kotlin.String  let 
kotlin.String  split 
kotlin.String  toLong 
kotlin.String  message kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  contains kotlin.collections  
isNotEmpty kotlin.collections  
mutableListOf kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  get kotlin.collections.List  iterator kotlin.collections.List  size kotlin.collections.List  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  keys kotlin.collections.MutableMap  iterator kotlin.collections.MutableSet  
startsWith 	kotlin.io  java 
kotlin.jvm  contains 
kotlin.ranges  java kotlin.reflect.KClass  contains kotlin.sequences  contains kotlin.text  
isNotEmpty kotlin.text  	lowercase kotlin.text  split kotlin.text  
startsWith kotlin.text  toLong kotlin.text  GlobalScope kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  
JSONObject org.json  Notification android.app.Activity  
StringBuilder android.app.Activity  debugAllSettings android.app.Activity  packageName android.app.Activity   requestDisplayOverAppsPermission android.app.Activity  testImmediateAlarm android.app.Activity  setShowBadge android.app.NotificationChannel  Notification android.content.Context  
StringBuilder android.content.Context  debugAllSettings android.content.Context  packageName android.content.Context   requestDisplayOverAppsPermission android.content.Context  testImmediateAlarm android.content.Context  Notification android.content.ContextWrapper  
StringBuilder android.content.ContextWrapper  debugAllSettings android.content.ContextWrapper   requestDisplayOverAppsPermission android.content.ContextWrapper  testImmediateAlarm android.content.ContextWrapper  packageName android.content.Intent  MODEL android.os.Build  RELEASE android.os.Build.VERSION   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  canDrawOverlays android.provider.Settings  Notification  android.view.ContextThemeWrapper  
StringBuilder  android.view.ContextThemeWrapper  debugAllSettings  android.view.ContextThemeWrapper  packageName  android.view.ContextThemeWrapper   requestDisplayOverAppsPermission  android.view.ContextThemeWrapper  testImmediateAlarm  android.view.ContextThemeWrapper  debugAllSettings android.widget.Button   requestDisplayOverAppsPermission android.widget.Button  testImmediateAlarm android.widget.Button  setOnlyAlertOnce ,androidx.core.app.NotificationCompat.Builder  setShowWhen ,androidx.core.app.NotificationCompat.Builder  	setSilent ,androidx.core.app.NotificationCompat.Builder  
StringBuilder com.cyberself.copilot  debugAllSettings com.cyberself.copilot  packageName com.cyberself.copilot   requestDisplayOverAppsPermission com.cyberself.copilot  testImmediateAlarm com.cyberself.copilot  Notification 'com.cyberself.copilot.TestAlarmActivity  
StringBuilder 'com.cyberself.copilot.TestAlarmActivity  debugAllSettings 'com.cyberself.copilot.TestAlarmActivity   requestDisplayOverAppsPermission 'com.cyberself.copilot.TestAlarmActivity  testImmediateAlarm 'com.cyberself.copilot.TestAlarmActivity  
StringBuilder 	java.lang  append java.lang.StringBuilder  toString java.lang.StringBuilder  Handler android.app.Activity  Looper android.app.Activity  TAG android.app.Activity  tryAlternativeLockScreenBypass android.app.Activity  KeyguardDismissCallback $android.app.Activity.KeyguardManager  KeyguardDismissCallback android.app.KeyguardManager  isKeyguardLocked android.app.KeyguardManager  Log 3android.app.KeyguardManager.KeyguardDismissCallback  TAG 3android.app.KeyguardManager.KeyguardDismissCallback  tryAlternativeLockScreenBypass 3android.app.KeyguardManager.KeyguardDismissCallback  TAG android.content.Context  tryAlternativeLockScreenBypass android.content.Context  KeyguardDismissCallback 'android.content.Context.KeyguardManager  TAG android.content.ContextWrapper  tryAlternativeLockScreenBypass android.content.ContextWrapper  KeyguardDismissCallback .android.content.ContextWrapper.KeyguardManager  FLAG_ACTIVITY_BROUGHT_TO_FRONT android.content.Intent  FLAG_ACTIVITY_REORDER_TO_FRONT android.content.Intent  KITKAT android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  View android.view  Handler  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  tryAlternativeLockScreenBypass  android.view.ContextThemeWrapper  KeyguardDismissCallback 0android.view.ContextThemeWrapper.KeyguardManager  SYSTEM_UI_FLAG_FULLSCREEN android.view.View  SYSTEM_UI_FLAG_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_IMMERSIVE_STICKY android.view.View   SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN android.view.View  %SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_LAYOUT_STABLE android.view.View  requestFocus android.view.View  systemUiVisibility android.view.View  	decorView android.view.Window  FLAG_ALLOW_LOCK_WHILE_SCREEN_ON 'android.view.WindowManager.LayoutParams  FLAG_FULLSCREEN 'android.view.WindowManager.LayoutParams  FLAG_LAYOUT_IN_SCREEN 'android.view.WindowManager.LayoutParams  	Exception #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  tryAlternativeLockScreenBypass #androidx.activity.ComponentActivity  KeyguardDismissCallback 3androidx.activity.ComponentActivity.KeyguardManager  	Exception #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  tryAlternativeLockScreenBypass #androidx.core.app.ComponentActivity  KeyguardDismissCallback 3androidx.core.app.ComponentActivity.KeyguardManager  setTimeoutAfter ,androidx.core.app.NotificationCompat.Builder  TAG com.cyberself.copilot  tryAlternativeLockScreenBypass com.cyberself.copilot  android +com.cyberself.copilot.AlarmLauncherActivity  tryAlternativeLockScreenBypass +com.cyberself.copilot.AlarmLauncherActivity  createUrgentAlarmNotification "com.cyberself.copilot.AlarmService  executeUniversalAlarmStrategy "com.cyberself.copilot.AlarmService  KeyguardDismissCallback %com.cyberself.copilot.KeyguardManager  Handler 'com.cyberself.copilot.TestAlarmActivity  Looper 'com.cyberself.copilot.TestAlarmActivity  requestCriticalPermissions 'com.cyberself.copilot.TestAlarmActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    