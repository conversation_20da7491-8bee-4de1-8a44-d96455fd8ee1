package com.cyberself.copilot

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import android.os.Handler
import android.os.Looper
import android.app.NotificationManager
import android.os.PowerManager
import android.app.NotificationChannel
import android.app.Notification
import android.app.PendingIntent
import androidx.core.app.NotificationCompat


class AlarmReceiver : BroadcastReceiver() {
    private val TAG = "AlarmReceiver"

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Alarm received: ${intent.action}")

        // Acquire wake lock with screen wake capability
        val wakeLock = (context.getSystemService(Context.POWER_SERVICE) as PowerManager).run {
            newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK or
                PowerManager.ACQUIRE_CAUSES_WAKEUP or
                PowerManager.ON_AFTER_RELEASE,
                "com.cyberself.copilot:AlarmWakeLock"
            ).apply {
                acquire(15000L) // 15 seconds timeout - increased for reliability
            }
        }

        when (intent.action) {
            "com.cyberself.copilot.ALARM_TRIGGER" -> {
                val alarmData = intent.getStringExtra("ALARM_DATA") ?: return
                Log.d(TAG, "Processing alarm trigger with data: $alarmData")

                // Always start foreground service for maximum reliability
                startAlarmService(context, alarmData)

                // Release wake lock after a delay
                Handler(Looper.getMainLooper()).postDelayed({
                    if (wakeLock.isHeld) {
                        wakeLock.release()
                        Log.d(TAG, "Wake lock released")
                    }
                }, 5000) // Reduced delay since service will handle the rest
            }
        }
    }

    private fun startAlarmService(context: Context, alarmData: String) {
        Log.d(TAG, "Starting alarm service with data: $alarmData")

        try {
            val serviceIntent = Intent(context, AlarmService::class.java).apply {
                action = "com.cyberself.copilot.START_ALARM"
                putExtra("ALARM_DATA", alarmData)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
                Log.d(TAG, "Started foreground service")
            } else {
                context.startService(serviceIntent)
                Log.d(TAG, "Started regular service")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start alarm service, trying fallback", e)
            // Fallback to direct activity launch
            tryDirectActivityLaunch(context, alarmData)
        }
    }

    private fun tryDirectActivityLaunch(context: Context, alarmData: String) {
        val activityIntent = Intent(context, AlarmLauncherActivity::class.java).apply {
            addFlags(
                Intent.FLAG_ACTIVITY_NEW_TASK or
                Intent.FLAG_ACTIVITY_CLEAR_TOP or
                Intent.FLAG_ACTIVITY_SINGLE_TOP or
                Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS or
                Intent.FLAG_ACTIVITY_NO_HISTORY
            )
            putExtra("LAUNCH_ALARM_SCREEN", true)
            putExtra("ALARM_DATA", alarmData)
        }

        try {
            context.startActivity(activityIntent)
            Log.d(TAG, "Direct activity launch successful")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start activity directly, using full-screen notification", e)
            showFullScreenNotification(context, alarmData)
        }
    }

    private fun showFullScreenNotification(context: Context, alarmData: String) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create notification channel
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "alarm_channel_fullscreen",
                "Alarm Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Alarm notifications with full screen"
                setBypassDnd(true)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                setSound(null, null)
            }
            notificationManager.createNotificationChannel(channel)
        }

        // Create full-screen intent
        val fullScreenIntent = Intent(context, AlarmLauncherActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_SINGLE_TOP)
            putExtra("LAUNCH_ALARM_SCREEN", true)
            putExtra("ALARM_DATA", alarmData)
        }

        val fullScreenPendingIntent = PendingIntent.getActivity(
            context,
            System.currentTimeMillis().toInt(),
            fullScreenIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build notification with aggressive settings for immediate display
        val notification = NotificationCompat.Builder(context, "alarm_channel_fullscreen")
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm)
            .setContentTitle("Alarm")
            .setContentText("Tap to view alarm")
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setFullScreenIntent(fullScreenPendingIntent, true)
            .setAutoCancel(true)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()

        // Use FLAG_INSISTENT to keep notifying until handled
        notification.flags = notification.flags or Notification.FLAG_INSISTENT

        // Show notification
        notificationManager.notify(ALARM_NOTIFICATION_ID, notification)
    }

    companion object {
        private const val ALARM_NOTIFICATION_ID = 7777
    }
}