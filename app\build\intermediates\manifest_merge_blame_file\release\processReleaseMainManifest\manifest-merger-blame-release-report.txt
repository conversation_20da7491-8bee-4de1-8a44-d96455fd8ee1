1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cyberself.copilot"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:4:5-66
11-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:5:3-63
12-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:5:20-61
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:6:3-77
13-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:6:20-74
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- For Android 12+ -->
14-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:7:3-72
14-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:7:20-69
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:8:3-75
15-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:8:20-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:9:3-87
16-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:9:20-84
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:10:3-79
17-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:10:20-76
18    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
18-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:11:3-74
18-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:11:20-71
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:12:3-66
19-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:12:20-63
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:13:3-77
20-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:13:20-75
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:14:3-76
21-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:14:20-73
22    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
22-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:15:3-79
22-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:15:20-76
23    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
23-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:16:3-71
23-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:16:20-68
24    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
24-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:17:3-73
24-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:17:20-70
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18:3-77
25-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:18:20-75
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:19:3-68
26-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:19:20-66
27    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
27-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:20:3-69
27-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:20:20-67
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21:3-78
28-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:21:20-76
29    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
29-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:22:3-71
29-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:22:20-69
30    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
30-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:23:3-93
30-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:23:20-90
31    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
31-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:24:3-95
31-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:24:20-92
32    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
32-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:25:3-73
32-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:25:20-70
33    <uses-permission android:name="com.xiaomi.permission.AUTOSTART" />
33-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:26:3-69
33-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:26:20-66
34    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
34-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:27:3-73
34-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:27:20-70
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required for Android 13+ -->
35-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:28:3-74
35-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:28:20-72
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" /> <!-- Android 14+ -->
36-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:29:3-91
36-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:29:20-88
37    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" /> <!-- For better wake detection -->
37-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:30:3-83
37-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:30:20-80
38    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
38-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:31:3-83
38-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:31:20-80
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- For DND bypass -->
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45    <application
45-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:33:5-102:19
46        android:allowBackup="true"
46-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:34:9-35
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb4d84fca6908b8a011123061ad45a8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:dataExtractionRules="@xml/data_extraction_rules"
48-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:35:9-65
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:36:9-54
51        android:icon="@mipmap/ic_launcher"
51-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:37:9-43
52        android:label="@string/app_name"
52-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:38:9-41
53        android:roundIcon="@mipmap/ic_launcher_round"
53-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:39:9-54
54        android:supportsRtl="true"
54-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:40:9-35
55        android:theme="@style/Theme.AlarmTest" >
55-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:41:9-47
56        <activity
56-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:43:9-47:54
57            android:name="com.cyberself.copilot.MainActivity"
57-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:44:13-41
58            android:exported="true"
58-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:45:13-36
59            android:label="@string/app_name"
59-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:46:13-45
60            android:theme="@style/Theme.AlarmTest" />
60-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:47:13-51
61
62        <!-- Test Activity for Alarm Testing -->
63        <activity
63-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:50:9-60:20
64            android:name="com.cyberself.copilot.TestAlarmActivity"
64-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:51:13-46
65            android:exported="true"
65-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:52:13-36
66            android:label="Alarm Test"
66-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:53:13-39
67            android:theme="@style/Theme.AlarmTest" >
67-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:54:13-51
68            <intent-filter>
68-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:55:13-59:29
69                <action android:name="android.intent.action.MAIN" />
69-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:56:17-69
69-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:56:25-66
70
71                <category android:name="android.intent.category.LAUNCHER" />
71-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:58:17-77
71-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:58:27-74
72            </intent-filter>
73        </activity>
74
75        <!-- Alarm Launcher Activity - Displays when alarm triggers -->
76        <activity
76-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:63:9-70:54
77            android:name="com.cyberself.copilot.AlarmLauncherActivity"
77-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:64:13-50
78            android:excludeFromRecents="true"
78-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:66:13-46
79            android:exported="true"
79-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:65:13-36
80            android:launchMode="singleTop"
80-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:67:13-43
81            android:showOnLockScreen="true"
81-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:68:13-44
82            android:theme="@style/Theme.AlarmTest"
82-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:70:13-51
83            android:turnScreenOn="true" />
83-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:69:13-40
84
85        <!-- Alarm Receiver - Handles alarm broadcasts -->
86        <receiver
86-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:73:9-76:39
87            android:name="com.cyberself.copilot.AlarmReceiver"
87-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:74:13-42
88            android:enabled="true"
88-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:75:13-35
89            android:exported="true" />
89-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:76:13-36
90
91        <!-- Boot Receiver - Restores alarms after device restart -->
92        <receiver
92-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:79:9-90:20
93            android:name="com.cyberself.copilot.BootReceiver"
93-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:80:13-41
94            android:enabled="true"
94-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:81:13-35
95            android:exported="true" >
95-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:82:13-36
96            <intent-filter android:priority="1000" >
96-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:83:13-89:29
96-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:83:28-51
97                <action android:name="android.intent.action.BOOT_COMPLETED" />
97-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:84:17-79
97-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:84:25-76
98                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
98-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:85:17-82
98-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:85:25-79
99                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
99-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:86:17-84
99-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:86:25-81
100                <action android:name="android.intent.action.PACKAGE_REPLACED" />
100-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:87:17-81
100-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:87:25-78
101
102                <data android:scheme="package" />
102-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:88:17-50
102-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:88:23-47
103            </intent-filter>
104        </receiver>
105
106        <!-- Alarm Service - Foreground service for alarm management -->
107        <service
107-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:93:9-101:19
108            android:name="com.cyberself.copilot.AlarmService"
108-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:94:13-41
109            android:enabled="true"
109-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:95:13-35
110            android:exported="false"
110-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:96:13-37
111            android:foregroundServiceType="specialUse" >
111-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:97:13-55
112            <property
112-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:98:13-100:41
113                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
113-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:99:17-76
114                android:value="alarm" />
114-->D:\PROJECTS\alarmtest\app\src\main\AndroidManifest.xml:100:17-38
115        </service>
116
117        <provider
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
118            android:name="androidx.startup.InitializationProvider"
118-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
119            android:authorities="com.cyberself.copilot.androidx-startup"
119-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
120            android:exported="false" >
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
121            <meta-data
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.emoji2.text.EmojiCompatInitializer"
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
123                android:value="androidx.startup" />
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
125-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
126                android:value="androidx.startup" />
126-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d0b42d8830df323609b43c692a043b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
129                android:value="androidx.startup" />
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
130        </provider>
131
132        <receiver
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
133            android:name="androidx.profileinstaller.ProfileInstallReceiver"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
134            android:directBootAware="false"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
135            android:enabled="true"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
136            android:exported="true"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
137            android:permission="android.permission.DUMP" >
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
139                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
142                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
145                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
146            </intent-filter>
147            <intent-filter>
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
148                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e38a164bacc08b11cd464fecb5d660c6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
149            </intent-filter>
150        </receiver>
151    </application>
152
153</manifest>
