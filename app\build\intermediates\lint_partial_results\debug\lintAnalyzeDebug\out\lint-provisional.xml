<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="18"
            column="34"
            startOffset="1270"
            endLine="18"
            endColumn="74"
            endOffset="1310"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="21"
            column="34"
            startOffset="1487"
            endLine="21"
            endColumn="75"
            endOffset="1528"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ExactAlarm"
        severity="error"
        message="`USE_EXACT_ALARM` can only be used when targeting API level 33 or higher">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="34"
            startOffset="405"
            endLine="7"
            endColumn="68"
            endOffset="439"/>
        <map>
            <condition targetLT="33"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/AlarmReceiver.kt"
            line="151"
            column="9"
            startOffset="6050"
            endLine="151"
            endColumn="72"
            endOffset="6113"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/AlarmService.kt"
            line="242"
            column="13"
            startOffset="9665"
            endLine="242"
            endColumn="83"
            endOffset="9735"/>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/cyberself/copilot/TestAlarmActivity.kt"
            line="265"
            column="46"
            startOffset="11129"
            endLine="265"
            endColumn="89"
            endOffset="11172"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="HighSamplingRate"
        severity="warning"
        message="Most apps don&apos;t need access to high sensor sampling rate.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="30"
            column="34"
            startOffset="2268"
            endLine="30"
            endColumn="79"
            endOffset="2313"/>
        <map>
            <condition targetGE="31"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="2872"
                endOffset="2892"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="42"
            column="9"
            startOffset="2872"
            endLine="42"
            endColumn="29"
            endOffset="2892"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
