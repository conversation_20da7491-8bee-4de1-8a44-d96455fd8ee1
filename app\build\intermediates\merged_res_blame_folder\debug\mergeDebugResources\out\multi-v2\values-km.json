{"logs": [{"outputFile": "com.cyberself.copilot.app-mergeDebugResources-44:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8cb4d84fca6908b8a011123061ad45a8\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,303,401,501,602,714,8356", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "195,298,396,496,597,709,821,8452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8725,8816", "endColumns": "90,91", "endOffsets": "8811,8903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,979,1064,1140,1214,1286,1357,1441,1507", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,974,1059,1135,1209,1281,1352,1436,1502,1620"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,911,991,1095,1193,1281,1365,7726,7811,7898,7978,8063,8139,8213,8285,8457,8541,8607", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "906,986,1090,1188,1276,1360,1443,7806,7893,7973,8058,8134,8208,8280,8351,8536,8602,8720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4680,4768,4856,4957,5037,5121,5221,5323,5419,5528,5615,5720,5818,5929,6046,6126,6233", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4675,4763,4851,4952,5032,5116,5216,5318,5414,5523,5610,5715,5813,5924,6041,6121,6228,6328"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1448,1566,1685,1791,1907,2009,2115,2238,2382,2510,2662,2753,2853,2953,3063,3187,3312,3417,3543,3669,3797,3959,4081,4195,4308,4431,4532,4632,4758,4897,5001,5106,5218,5343,5471,5588,5696,5772,5869,5965,6073,6161,6249,6350,6430,6514,6614,6716,6812,6921,7008,7113,7211,7322,7439,7519,7626", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "1561,1680,1786,1902,2004,2110,2233,2377,2505,2657,2748,2848,2948,3058,3182,3307,3412,3538,3664,3792,3954,4076,4190,4303,4426,4527,4627,4753,4892,4996,5101,5213,5338,5466,5583,5691,5767,5864,5960,6068,6156,6244,6345,6425,6509,6609,6711,6807,6916,7003,7108,7206,7317,7434,7514,7621,7721"}}]}]}